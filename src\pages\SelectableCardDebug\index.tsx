import { SelectableGradientCard } from '@/components/Card'
import { motion } from 'framer-motion'
import { Bookmark, Calendar, Download, Eye, Heart, MessageCircle, Settings, Share, Star, Tag, ThumbsUp, User } from 'lucide-react'
import { useState } from 'react'

/** 生成测试卡片数据 */
function generateTestCards() {
  return [
  // 1. 基础文本卡片
    {
      id: 'basic-text',
      title: '基础文本卡片',
      description: '这是一个基础的文本卡片，展示最简单的使用方式。',
      content: (
        <div className="p-6">
          <h3 className="mb-2 text-lg text-gray-900 font-semibold">基础卡片</h3>
          <p className="text-gray-600">这是卡片的内容区域，可以放置任何内容。</p>
        </div>
      ),
    },

    // 2. 带图标的卡片
    {
      id: 'icon-card',
      title: '带图标的卡片',
      description: '展示如何在卡片中使用图标。',
      content: (
        <div className="flex items-center gap-4 p-6">
          <div className="rounded-full bg-blue-100 p-3">
            <Star className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg text-gray-900 font-semibold">收藏功能</h3>
            <p className="text-gray-600">点击收藏您喜欢的内容</p>
          </div>
        </div>
      ),
    },

    // 3. 复杂内容卡片
    {
      id: 'complex-card',
      title: '复杂内容卡片',
      description: '展示复杂布局和多种元素的组合。',
      content: (
        <div className="p-6">
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 flex items-center justify-center rounded-full from-purple-400 to-blue-400 bg-gradient-to-r">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-gray-900 font-semibold">用户卡片</h3>
                <p className="text-sm text-gray-500">在线状态</p>
              </div>
            </div>
            <div className="flex gap-2">
              <button className="rounded-full p-2 hover:bg-gray-100">
                <Heart className="h-4 w-4 text-gray-400" />
              </button>
              <button className="rounded-full p-2 hover:bg-gray-100">
                <Share className="h-4 w-4 text-gray-400" />
              </button>
            </div>
          </div>
          <div className="flex gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>1.2k</span>
            </div>
            <div className="flex items-center gap-1">
              <ThumbsUp className="h-4 w-4" />
              <span>89</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="h-4 w-4" />
              <span>23</span>
            </div>
          </div>
        </div>
      ),
    },

    // 4. 自定义渲染卡片
    {
      id: 'custom-render',
      title: '自定义渲染卡片',
      description: '使用 renderContent 属性进行自定义渲染。',
      useCustomRender: true,
      renderContent: ({ isHovered, isSelected, contentBackground, onMouseEnter, onMouseLeave }) => (
        <div
          className="relative overflow-hidden transition-all duration-300"
          style={ { background: contentBackground } }
          onMouseEnter={ onMouseEnter }
          onMouseLeave={ onMouseLeave }
        >
          <div className="p-6">
            <div className="mb-3 flex items-center justify-between">
              <h3 className="text-gray-900 font-semibold">自定义渲染</h3>
              <div className={ `transition-all duration-300 ${isSelected
                ? 'text-purple-600'
                : isHovered
                  ? 'text-blue-600'
                  : 'text-gray-400'}` }>
                <Settings className="h-5 w-5" />
              </div>
            </div>
            <p className="mb-4 text-gray-600">
              当前状态:
              {' '}
              {isSelected
                ? '已选中'
                : isHovered
                  ? '悬浮中'
                  : '默认'}
            </p>
            <div className="flex gap-2">
              <span className={ `rounded px-2 py-1 text-xs ${isSelected
                ? 'bg-purple-100 text-purple-700'
                : 'bg-gray-100 text-gray-600'}` }>
                选中:
                {' '}
                {isSelected
                  ? '是'
                  : '否'}
              </span>
              <span className={ `rounded px-2 py-1 text-xs ${isHovered
                ? 'bg-blue-100 text-blue-700'
                : 'bg-gray-100 text-gray-600'}` }>
                悬浮:
                {' '}
                {isHovered
                  ? '是'
                  : '否'}
              </span>
            </div>
          </div>
        </div>
      ),
    },

    // 5. 不同尺寸的卡片
    {
      id: 'small-card',
      title: '小尺寸卡片',
      description: '展示紧凑布局的小卡片。',
      size: 'small',
      content: (
        <div className="p-4">
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-900 font-medium">标签</span>
          </div>
        </div>
      ),
    },

    // 6. 大尺寸卡片
    {
      id: 'large-card',
      title: '大尺寸卡片',
      description: '展示更多内容的大卡片。',
      size: 'large',
      content: (
        <div className="p-8">
          <div className="text-center">
            <div className="mx-auto mb-4 h-16 w-16 flex items-center justify-center rounded-full from-green-400 to-blue-500 bg-gradient-to-r">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <h3 className="mb-2 text-xl text-gray-900 font-bold">大型卡片</h3>
            <p className="mb-4 text-gray-600">这是一个大尺寸的卡片，可以容纳更多的内容和更复杂的布局。</p>
            <div className="flex justify-center gap-2">
              <button className="rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600">
                主要操作
              </button>
              <button className="border border-gray-300 rounded-lg px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50">
                次要操作
              </button>
            </div>
          </div>
        </div>
      ),
    },
  ]
}

export default function SelectableCardDebug() {
  const [selectedCards, setSelectedCards] = useState<Set<string>>(new Set())
  const [darkMode, setDarkMode] = useState(false)
  const [singleSelectMode, setSingleSelectMode] = useState(false)
  const [customBorderGradient, setCustomBorderGradient] = useState('linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)')
  const [customHoverBg, setCustomHoverBg] = useState('linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)')

  const testCards = generateTestCards()

  const handleCardSelect = (cardId: string, selected: boolean) => {
    if (singleSelectMode) {
      /** 单选模式 */
      setSelectedCards(selected
        ? new Set([cardId])
        : new Set())
    }
    else {
      /** 多选模式 */
      const newSelected = new Set(selectedCards)
      if (selected) {
        newSelected.add(cardId)
      }
      else {
        newSelected.delete(cardId)
      }
      setSelectedCards(newSelected)
    }
  }

  const clearSelection = () => {
    setSelectedCards(new Set())
  }

  const selectAll = () => {
    setSelectedCards(new Set(testCards.map(card => card.id)))
  }

  return (
    <div className={ `min-h-screen transition-colors ${darkMode
      ? 'dark bg-gray-900'
      : 'bg-gray-50'}` }>
      {/* 控制面板 */}
      <div className="sticky top-0 z-10 border-b border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div className="mx-auto max-w-6xl">
          <div className="mb-4 flex items-center justify-between">
            <h1 className="text-2xl text-gray-900 font-bold dark:text-white">
              SelectableGradientCard 组件调试
            </h1>
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                <input
                  type="checkbox"
                  checked={ darkMode }
                  onChange={ e => setDarkMode(e.target.checked) }
                  className="rounded"
                />
                暗黑模式
              </label>
              <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                <input
                  type="checkbox"
                  checked={ singleSelectMode }
                  onChange={ e => setSingleSelectMode(e.target.checked) }
                  className="rounded"
                />
                单选模式
              </label>
            </div>
          </div>

          {/* 选择状态和操作 */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              已选中:
              {' '}
              {selectedCards.size}
              {' '}
              /
              {' '}
              {testCards.length}
              {' '}
              个卡片
              {selectedCards.size > 0 && (
                <span className="ml-2">
                  [
                  {Array.from(selectedCards).join(', ')}
                  ]
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <button
                onClick={ clearSelection }
                className="rounded bg-gray-200 px-3 py-1 text-sm text-gray-700 dark:bg-gray-700 hover:bg-gray-300 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                清空选择
              </button>
              {!singleSelectMode && (
                <button
                  onClick={ selectAll }
                  className="rounded bg-blue-500 px-3 py-1 text-sm text-white hover:bg-blue-600"
                >
                  全选
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 自定义样式控制 */}
      <div className="mx-auto max-w-6xl px-6 py-4">
        <div className="border border-gray-200 rounded-lg bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
          <h3 className="mb-4 text-lg text-gray-900 font-semibold dark:text-white">自定义样式</h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-2 block text-sm text-gray-700 font-medium dark:text-gray-300">
                边框渐变
              </label>
              <select
                value={ customBorderGradient }
                onChange={ e => setCustomBorderGradient(e.target.value) }
                className="w-full border border-gray-300 rounded-md bg-white p-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)">紫蓝渐变（默认）</option>
                <option value="linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%)">红绿渐变</option>
                <option value="linear-gradient(90deg, #FFD93D 0%, #FF6B6B 100%)">黄红渐变</option>
                <option value="linear-gradient(90deg, #6C5CE7 0%, #A29BFE 100%)">紫色渐变</option>
                <option value="linear-gradient(90deg, #00B894 0%, #00CEC9 100%)">青色渐变</option>
              </select>
            </div>
            <div>
              <label className="mb-2 block text-sm text-gray-700 font-medium dark:text-gray-300">
                悬浮背景
              </label>
              <select
                value={ customHoverBg }
                onChange={ e => setCustomHoverBg(e.target.value) }
                className="w-full border border-gray-300 rounded-md bg-white p-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)">淡紫蓝（默认）</option>
                <option value="linear-gradient(90deg, rgba(255, 107, 107, 0.10) 0%, rgba(78, 205, 196, 0.10) 100%)">淡红绿</option>
                <option value="linear-gradient(90deg, rgba(255, 217, 61, 0.10) 0%, rgba(255, 107, 107, 0.10) 100%)">淡黄红</option>
                <option value="rgba(108, 92, 231, 0.10)">淡紫色</option>
                <option value="rgba(0, 184, 148, 0.10)">淡青色</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* 卡片展示区域 */}
      <div className="mx-auto max-w-6xl p-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3 md:grid-cols-2">
          {testCards.map((card, index) => (
            <motion.div
              key={ card.id }
              initial={ { opacity: 0, y: 20 } }
              animate={ { opacity: 1, y: 0 } }
              transition={ { delay: index * 0.1 } }
              className="space-y-3"
            >
              {/* 卡片信息 */}
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <div className="font-medium">{card.title}</div>
                <div className="text-xs">{card.description}</div>
                {card.size && (
                  <div className="text-xs text-blue-600 dark:text-blue-400">
                    尺寸:
                    {card.size}
                  </div>
                )}
              </div>

              {/* SelectableGradientCard */}
              <SelectableGradientCard
                className={ `  ${card.size === 'small'
                  ? 'max-w-xs'
                  : ''}  ${card.size === 'large'
                  ? 'col-span-full max-w-md mx-auto'
                  : ''}  ` }
                borderGradient={ customBorderGradient }
                hoverBackground={ customHoverBg }
                selectedBackground={ customHoverBg }
                selected={ selectedCards.has(card.id) }
                onSelectedChange={ selected => handleCardSelect(card.id, selected) }
                renderContent={ card.useCustomRender
                  ? card.renderContent
                  : undefined }
              >
                {!card.useCustomRender && card.content}
              </SelectableGradientCard>
            </motion.div>
          ))}
        </div>
      </div>

      {/* 组件状态说明 */}
      <div className="mx-auto mt-8 max-w-6xl p-6">
        <div className="border border-gray-200 rounded-lg bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
          <h2 className="mb-6 text-xl text-gray-900 font-semibold dark:text-white">
            SelectableGradientCard 组件状态说明
          </h2>

          <div className="grid grid-cols-1 gap-6 text-sm lg:grid-cols-3 md:grid-cols-2">
            <div>
              <h3 className="mb-3 text-gray-900 font-semibold dark:text-white">基础属性</h3>
              <ul className="text-gray-600 space-y-2 dark:text-gray-400">
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">borderWidth</code>
                  : 边框宽度（默认1.5px）
                </li>
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">borderGradient</code>
                  : 边框渐变色
                </li>
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">className</code>
                  : 自定义样式类
                </li>
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">children</code>
                  : 子元素内容
                </li>
              </ul>
            </div>

            <div>
              <h3 className="mb-3 text-gray-900 font-semibold dark:text-white">交互状态</h3>
              <ul className="text-gray-600 space-y-2 dark:text-gray-400">
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">selected</code>
                  : 选中状态
                </li>
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">onSelectedChange</code>
                  : 选中状态变化回调
                </li>
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">hoverBackground</code>
                  : 悬浮背景色
                </li>
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">selectedBackground</code>
                  : 选中背景色
                </li>
              </ul>
            </div>

            <div>
              <h3 className="mb-3 text-gray-900 font-semibold dark:text-white">高级功能</h3>
              <ul className="text-gray-600 space-y-2 dark:text-gray-400">
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">renderContent</code>
                  : 自定义渲染函数
                </li>
                <li>
                  •
                  <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">contentClassName</code>
                  : 内容容器样式
                </li>
                <li>• 支持所有标准 div 属性</li>
                <li>• 自动处理悬浮和选中状态</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
            <h4 className="mb-2 text-blue-900 font-semibold dark:text-blue-100">使用提示</h4>
            <ul className="text-sm text-blue-800 space-y-1 dark:text-blue-200">
              <li>• 渐变边框始终显示，不受状态影响</li>
              <li>• 悬浮和选中状态只影响内容区域背景</li>
              <li>• 使用 renderContent 可以完全自定义内容渲染逻辑</li>
              <li>• 支持单选和多选模式（通过父组件管理）</li>
              <li>• 所有状态变化都有平滑的过渡动画</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
