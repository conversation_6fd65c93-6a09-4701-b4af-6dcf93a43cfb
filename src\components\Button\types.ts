import type { VariantProps } from 'class-variance-authority'
import type { ReactNode } from 'react'
import type { buttonVariants } from './styles'

export type ButtonDesignStyle = 'flat' | 'neumorphic' | 'outlined' | 'ghost'

export type ButtonVariant = 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info'

export type ButtonProps = React.PropsWithChildren<React.ButtonHTMLAttributes<HTMLButtonElement>>
  & VariantProps<typeof buttonVariants> & {
    leftIcon?: ReactNode

    rightIcon?: ReactNode

    iconOnly?: boolean

    loading?: boolean

    loadingText?: string

    disabled?: boolean

    designStyle?: ButtonDesignStyle

    block?: boolean

    hoverClassName?: string

    activeClassName?: string

    disabledClassName?: string

    loadingClassName?: string

    iconClassName?: string

    onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void

    asChild?: boolean
    as?: React.ElementType
  }
