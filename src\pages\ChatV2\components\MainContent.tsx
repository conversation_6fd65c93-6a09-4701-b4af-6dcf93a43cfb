/**
 * MainContent - 主内容区组件
 * 包含Welcome界面和ChatWorkflow界面的切换
 * 高度可配置，支持自定义数据和事件处理
 */

import type { MarketStep1Params } from '@/api/MarketApi'
import { Animate } from '@/components/Animate'
import { Button } from 'antd'
import cn from 'clsx'
import cx from 'clsx'
import React, { useCallback, useState } from 'react'
import { ChatEventBus } from '../constants'
import { ChatHistory } from './ChatHistory'
import { ChatInput } from './ChatInput'
import ChatWorkflow from './ChatWorkflow'
import ProductVisualizer from './ProductVisualizer'
import { Step } from './Step'

// ==================== 类型定义 ====================

/**
 * 功能按钮配置
 */
export interface QuickActionButton {
  id: string
  label: string
  onClick?: () => void
}

/**
 * 代理图标配置
 */
export interface AgentIcon {
  name: string
  imageSrc: string
  altText?: string
}

/**
 * 主内容区配置Props
 */
export interface MainContentProps {
  // ========== 模式控制 ==========
  openWorkflow?: boolean
  isGenerating?: boolean

  // ========== Welcome界面配置 ==========
  welcomeTitle?: string
  welcomeDescription?: string
  welcomeTitleStyle?: React.CSSProperties
  agentIcons?: AgentIcon[]
  quickActionButtons?: QuickActionButton[]
  selectedButton?: string | null
  onButtonSelect?: (buttonId: string) => void

  // ========== 输入区域配置 ==========
  message?: string
  onMessageChange?: (message: string) => void
  uploadedImage?: string | null
  onImageUpload?: (file: File) => void
  onImageRemove?: () => void
  urlInputValue?: string
  onUrlInputChange?: (value: string) => void
  inputPlaceholder?: string
  generateButtonText?: string
  generatingButtonText?: string
  onGenerate?: () => void

  // ========== ChatWorkflow配置 ==========
  showWorkflow?: boolean
  showForm?: boolean
  isThinking?: boolean
  thinkingExpanded?: boolean
  onThinkingToggle?: (expanded: boolean) => void
  contentDescription?: string
  formData?: Partial<MarketStep1Params>
  formErrors?: Record<string, string>
  formUploadedImage?: string | null
  isSubmitting?: boolean
  onFormChange?: (field: keyof MarketStep1Params, value: string) => void
  onFormSubmit?: () => void | Promise<void>
  onFormImageUpload?: (file: File) => void
  onFormImageRemove?: () => void

  // ========== 历史聊天配置 ==========
  messages?: any[]
  onLoadMore?: () => void
  hasMore?: boolean
  showStep?: boolean
  enableInput?: boolean
  sendButtonText?: string
  onSendMsg?: (...data: any[]) => void

  // ========== 样式配置 ==========
  className?: string
  style?: React.CSSProperties
  containerClassName?: string
  containerStyle?: React.CSSProperties
}

// ==================== 默认配置 ====================

const defaultAgentIcons: AgentIcon[] = [
  { name: 'Research Analyst', imageSrc: '/src/assets/image/home/<USER>' },
  { name: 'Brand Strategist', imageSrc: '/src/assets/image/home/<USER>' },
  { name: 'Creative Director', imageSrc: '/src/assets/image/home/<USER>' },
  { name: 'Operations Manager', imageSrc: '/src/assets/image/home/<USER>' },
]

const defaultQuickActionButtons: QuickActionButton[] = [
  { id: 'create-post', label: 'Create Post' },
  { id: 'generate-image', label: 'Generate Image' },
  { id: 'generate-video', label: 'Generate Video' },
  { id: 'account-analytics', label: 'Account Analytics' },
  { id: 'analyze-trending', label: 'Analyze Trending Post' },
  { id: 'comparison-post', label: 'Comparison Post' },
]

// ==================== 主组件 ====================

/**
 * MainContent 主组件
 */
export const MainContent: React.FC<MainContentProps> = ({
  /** 模式控制 */
  openWorkflow = false,
  isGenerating = false,

  // Welcome界面
  welcomeTitle = 'Welcome to PhotoG!',
  welcomeDescription = 'An efficient AI-Marketing team solve all your marketing problems.',
  welcomeTitleStyle = {
    backgroundImage: 'linear-gradient(90deg, #CB6BFF 0%, #05C9FF 100%)',
    fontSize: 'clamp(1.25rem, 3.5vw, 3rem)',
  },
  agentIcons = defaultAgentIcons,
  quickActionButtons = defaultQuickActionButtons,
  selectedButton,
  onButtonSelect,

  /** 输入区域 */
  message = '',
  onMessageChange,
  uploadedImage,
  onImageUpload,
  onImageRemove,
  urlInputValue = '',
  onUrlInputChange,
  inputPlaceholder = 'Enter a description of your product',
  generateButtonText = 'Generate',
  generatingButtonText = 'Generating...',
  onGenerate,

  // ChatWorkflow
  showWorkflow = false,
  showForm = false,
  isThinking = false,
  thinkingExpanded = true,
  onThinkingToggle,
  contentDescription = '',
  formData = {},
  formErrors = {},
  formUploadedImage = null,
  isSubmitting = false,
  onFormChange,
  onFormSubmit,
  onFormImageUpload,
  onFormImageRemove,

  /** 历史聊天 */
  messages = [],
  onLoadMore,
  hasMore = false,
  showStep = false,
  enableInput = true,
  sendButtonText = 'Send',
  onSendMsg,

  /** 样式 */
  className = '',
  style = {},
  containerClassName = '',
  containerStyle = {},
}) => {
  // ========== 事件处理 ==========

  const handleImageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && onImageUpload) {
      onImageUpload(file)
    }
  }, [onImageUpload])

  const handleFormImageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && onFormImageUpload) {
      onFormImageUpload(file)
    }
  }, [onFormImageUpload])

  // ========== 渲染 ==========

  return (
    <div className={ cn('flex-1 min-h-0 bg-white', containerClassName) } style={ containerStyle }>
      {/* 内容区 */}
      {openWorkflow ? (
        <div className={ cx('flex h-full p-8 gap-8', 'flex-col lg:flex-row') }>
          <ProductVisualizer className="shrink-0 grow-0" />

          <div className="min-w-0 flex flex-1 flex-col gap-4">
            <ChatHistory
              messages={ messages }
              onLoadMore={ onLoadMore }
              hasMore={ hasMore }
              className="flex-1 border border-gray-200 rounded-xl bg-white"
            />

            {showStep && <Step />}

            <ChatInput
              disabled={ !enableInput }
              as={ Animate }
              requireFile={ false }
              variants={ {
                initial: { opacity: 0, x: 20, height: '0px' },
                animate: { opacity: 1, x: 0, height: '135px' },
                exit: { opacity: 0, x: 20, height: '0px' },
              } }
              onSendMsg={ (...data) => {
                if (onSendMsg) {
                  onSendMsg(...data)
                }
                else {
                  ChatEventBus.emit('changeChatResp', ...data)
                }
              } }
              SendButton={
                <Button
                  htmlType="submit"
                  type="primary"
                  disabled={ !enableInput }
                  className="rounded-lg px-6"
                >
                  {sendButtonText}
                </Button>
              }
            />
          </div>
        </div>
      ) : (
        <div className="h-full flex flex-col">
          {!isGenerating ? (
            /* 原始Welcome界面 */
            <div className="h-full flex items-center justify-center" style={ { padding: '3vh 2vw' } }>
              <div className="flex flex-col" style={ { height: 'min(75vh, 800px)', width: '90%', maxWidth: '1600px' } }>
                {/* Welcome 标题和图标区域 */}
                <div className="flex flex-col items-center justify-center" style={ { height: '40%', gap: 'calc(1vh + 0.5vw)' } }>
                  <h1
                    className="bg-clip-text text-transparent font-semibold"
                    style={ welcomeTitleStyle }
                  >
                    {welcomeTitle}
                  </h1>

                  {/* 功能图标网格 */}
                  <div className="flex items-center justify-center" style={ { gap: 'clamp(1rem, 3vw, 3rem)' } }>
                    {agentIcons.map((agent, index) => (
                      <div key={ index } className="flex flex-col items-center" style={ { gap: 'clamp(0.25rem, 0.5vh, 0.5rem)' } }>
                        <div
                          className="flex items-center justify-center overflow-hidden rounded-xl"
                          style={ { width: 'clamp(3rem, 5vw, 5rem)', height: 'clamp(3rem, 5vw, 5rem)' } }
                        >
                          <img
                            src={ agent.imageSrc }
                            alt={ agent.altText || agent.name }
                            className="h-full w-full object-contain"
                          />
                        </div>
                        <span className="text-center text-gray-600" style={ { fontSize: 'clamp(0.5rem, 0.9vw, 0.75rem)' } }>
                          {agent.name}
                        </span>
                      </div>
                    ))}
                  </div>

                  <p className="text-center" style={ { fontSize: '24px', padding: '0 2vw', color: '#434343' } }>
                    {welcomeDescription}
                  </p>
                </div>

                {/* 输入区域 */}
                <div className="flex flex-1 items-center justify-center" style={ { height: '60%', width: '100%', margin: '0 auto' } }>
                  <div className="rounded-xl bg-white shadow-sm" style={ { width: '100%', padding: '1.5vh 1.5vw', border: '1px solid #E5E7EB' } }>
                    {/* 输入框 */}
                    <textarea
                      placeholder={ inputPlaceholder }
                      className={ cn(
                        'w-full resize-none outline-none',
                        isGenerating
                          ? 'text-gray-400 placeholder-gray-300 bg-gray-50 cursor-not-allowed'
                          : 'text-gray-700 placeholder-gray-400',
                      ) }
                      style={ {
                        fontSize: 'clamp(0.75rem, 1.2vw, 1rem)',
                        height: '8vh',
                        minHeight: '60px',
                      } }
                      value={ message }
                      onChange={ e => onMessageChange?.(e.target.value) }
                      disabled={ isGenerating }
                    />

                    {/* Upload上传区域 */}
                    <div style={ { marginTop: '1vh' } }>
                      <label className="inline-block cursor-pointer">
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={ handleImageChange }
                        />
                        {uploadedImage
                          ? (
                              <div
                                className="relative overflow-hidden border border-gray-300 rounded-lg"
                                style={ { width: '6vw', height: '6vw', minWidth: '60px', minHeight: '60px' } }
                              >
                                <img src={ uploadedImage } alt="Preview" className="h-full w-full object-cover" />
                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 opacity-0 transition-all hover:bg-opacity-30 hover:opacity-100">
                                  <button
                                    type="button"
                                    onClick={ (e) => {
                                      e.preventDefault()
                                      onImageRemove?.()
                                    } }
                                    className="h-6 w-6 flex items-center justify-center rounded-full bg-white text-sm text-red-500 font-bold"
                                  >
                                    ×
                                  </button>
                                </div>
                              </div>
                            )
                          : (
                              <div
                                className="flex flex-col items-center justify-center border border-gray-300 rounded-lg border-dashed bg-gray-50 transition-colors hover:bg-gray-100"
                                style={ { width: '6vw', height: '6vw', minWidth: '60px', minHeight: '60px', gap: '0.5vh' } }
                              >
                                <div
                                  className="flex items-center justify-center rounded bg-blue-50"
                                  style={ { width: '2vw', height: '2vw', minWidth: '20px', minHeight: '20px' } }
                                >
                                  <svg
                                    style={ { width: '1vw', height: '1vw', minWidth: '10px', minHeight: '10px' } }
                                    viewBox="0 0 16 16"
                                    fill="none"
                                  >
                                    <path d="M8 4V12M4 8H12" stroke="#60A5FA" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                  </svg>
                                </div>
                                <span className="text-gray-500" style={ { fontSize: 'clamp(0.5rem, 0.8vw, 0.75rem)' } }>Upload</span>
                              </div>
                            )}
                      </label>
                    </div>

                    {/* 分隔线和其他功能区域 */}
                    <div className="border-t border-gray-200" style={ { marginTop: '1vh', paddingTop: '1vh' } }>
                      {/* Add URL输入框 */}
                      <div className="relative" style={ { marginBottom: '1vh' } }>
                        <div className="absolute -translate-y-1/2" style={ { left: '1vw', top: '50%' } }>
                          <svg
                            style={ { width: 'clamp(12px, 1vw, 14px)', height: 'clamp(12px, 1vw, 14px)' } }
                            viewBox="0 0 14 14"
                            fill="none"
                          >
                            <path d="M7 1V13M1 7H13" stroke="#60A5FA" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </div>
                        <input
                          type="text"
                          placeholder="Add URL"
                          value={ urlInputValue }
                          onChange={ e => onUrlInputChange?.(e.target.value) }
                          className="w-full border border-gray-200 rounded-lg outline-none focus:border-blue-400 placeholder-gray-400"
                          style={ {
                            paddingLeft: '2.5vw',
                            paddingRight: '1vw',
                            paddingTop: '0.5vh',
                            paddingBottom: '0.5vh',
                            fontSize: 'clamp(0.75rem, 1vw, 0.875rem)',
                          } }
                        />
                      </div>

                      {/* 快速按钮 */}
                      <div className="flex flex-wrap" style={ { gap: '0.8vw' } }>
                        {quickActionButtons.map(button => (
                          <button
                            key={ button.id }
                            className={ cn(
                              'font-bold rounded-lg transition-all relative overflow-hidden',
                              selectedButton === button.id
                                ? 'text-gray-700'
                                : 'text-gray-700 bg-gray-100',
                            ) }
                            style={ { padding: '0.5vh 1vw', fontSize: 'clamp(0.625rem, 0.9vw, 0.875rem)' } }
                            onClick={ () => {
                              onButtonSelect?.(button.id)
                              button.onClick?.()
                            } }
                          >
                            {selectedButton === button.id && (
                              <div
                                className="absolute inset-0 opacity-10"
                                style={ { background: 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)' } }
                              />
                            )}
                            <span className="relative">{button.label}</span>
                          </button>
                        ))}

                        <button
                          className={ cn(
                            'rounded-full transition-colors ml-auto',
                            (!message.trim() || isGenerating)
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : 'bg-black text-white hover:bg-gray-800',
                          ) }
                          style={ { padding: '0.5vh 1.5vw', fontSize: 'clamp(0.625rem, 0.9vw, 0.875rem)' } }
                          disabled={ !message.trim() || isGenerating }
                          onClick={ onGenerate }
                        >
                          {isGenerating
                            ? generatingButtonText
                            : generateButtonText}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Generate后的新界面 - 使用ChatWorkflow组件 */
            <ChatWorkflow
              showContentDisplay
              content={ contentDescription }
              uploadedImage={ uploadedImage }
              showThinking
              isThinking={ isThinking }
              thinkingExpanded={ thinkingExpanded }
              onThinkingToggle={ onThinkingToggle }
              showWorkflow={ showWorkflow }
              showForm={ showForm && !showWorkflow }
              formData={ formData }
              formErrors={ formErrors }
              formUploadedImage={ formUploadedImage }
              isSubmitting={ isSubmitting }
              onFormChange={ onFormChange }
              onFormSubmit={ onFormSubmit }
              onImageUpload={ onFormImageUpload }
              onImageRemove={ onFormImageRemove }
              showAskInput={ showForm || showWorkflow }
              askInputDisabled
              askInputPlaceholder="Ask what's on your mind"
            />
          )}
        </div>
      )}
    </div>
  )
}

export default MainContent
