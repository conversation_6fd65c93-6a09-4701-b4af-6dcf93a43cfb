import type { MarketStep1Params } from '@/api/MarketApi'
import type { ChatProps } from '../types'
import { MarketApi } from '@/api/MarketApi'
import { Loading } from '@/components/Loading'
import { useT } from '@/hooks'
import { memo, useCallback, useRef, useState } from 'react'
import { chatStore } from '../store'
import MainContent from './MainContent'
import TopBar from './TopBar'

export const Chat = memo<ChatProps>(({
  messages,
  onLoadMore,
  setMessages,
  hasMore,
  className,
}) => {
  const t = useT()
  const snap = chatStore.use()
  const [message, setMessage] = useState('')
  const [isProjectExpanded, setIsProjectExpanded] = useState(false)
  const [selectedButton, setSelectedButton] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isThinking, setIsThinking] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showWorkflow, setShowWorkflow] = useState(false) // 新增工作流状态
  const [uploadedImage, setUploadedImage] = useState<string | null>(null)
  const [formUploadedImage, setFormUploadedImage] = useState<string | null>(null) // 表单独立的图片状态
  const [contentDescription, setContentDescription] = useState('')
  const [formData, setFormData] = useState<Partial<MarketStep1Params>>({
    brand: '',
    product_name: '',
    industry: '',
    competitor: '',
    product: '',
    pic: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const projectSidebarRef = useRef<any>(null)
  const marketApiRef = useRef(new MarketApi())

  const handleFormSubmit = async () => {
    if (isSubmitting)
      return

    /** 清除之前的错误 */
    setFormErrors({})

    /** 验证必填字段 */
    const errors: Record<string, string> = {}
    const requiredFields = [
      { key: 'brand', label: 'Please enter the content to continue.' },
      { key: 'product_name', label: 'Please enter the content to continue.' },
      { key: 'industry', label: 'Please enter the content to continue.' },
      { key: 'competitor', label: 'Please enter the information to continue.' },
      { key: 'product', label: 'Please enter the information to continue.' },
    ]

    requiredFields.forEach(({ key, label }) => {
      if (!formData[key as keyof MarketStep1Params]?.trim()) {
        errors[key] = `${label.toLowerCase()}`
      }
    })

    if (!formUploadedImage) {
      errors.pic = 'Please upload product image'
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)
    try {
      /** 准备API调用参数 */
      const params: MarketStep1Params = {
        industry: formData.industry!,
        industry_id: 83, // 默认行业ID，可以根据需要修改
        product_name: formData.product_name!,
        ip: '专研彩妆', // 默认值，可以根据需要修改
        brand: formData.brand!,
        role: '中国时尚彩妆领导品牌', // 默认值，可以根据需要修改
        product: formData.product!,
        pic: formUploadedImage || '/xxx.png',
        competitor: formData.competitor,
        company: formData.brand, // 使用品牌名作为公司名
      }

      /** 调用API */
      const result = await marketApiRef.current.executeAndStream(
        {
          processInstanceId: '',
          workflowStep: 'step1',
          params,
        },
        (data) => {
          /** 处理流式响应数据 */
          console.log('Stream data:', data)
        },
      )

      if (!result.hasError) {
        /** 成功后的处理 */
        console.log('API调用成功:', result)
        /** 切换到工作流界面 */
        setShowForm(false)
        setShowWorkflow(true)
      }
      else {
        alert(`处理流式响应数据: ${result.errorMsg}`)
        /** 切换到工作流界面 */
        setShowForm(false)
        setShowWorkflow(true)
      }
    }
    catch (error) {
      console.error('API调用失败:', error)
      alert('网络错误，请稍后重试')
    }
    finally {
      setIsSubmitting(false)
    }
  }

  const handleFormChange = (field: keyof MarketStep1Params, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    /** 清除该字段的错误状态 */
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  /** 点击外部关闭下拉菜单 */
  const handleClickOutside = useCallback((e: MouseEvent) => {
    const target = e.target as HTMLElement
    /** 检查点击是否在下拉菜单外部 */
    if (isProjectExpanded
      && !target.closest('.project-dropdown')
      && !target.closest('.project-toggle-button')) {
      setIsProjectExpanded(false)
    }
  }, [isProjectExpanded])

  return <div
    className="h-full w-full flex flex-col bg-white"
  >
    <Loading loading={ snap.sendLoading }></Loading>

    {/* 顶部栏 - 使用TopBar组件 */}
    <TopBar
      currentProject={ { id: '1', name: 'Project Name Goese Here' } }
      dropdownExpanded={ isProjectExpanded }
      onDropdownToggle={ setIsProjectExpanded }
      setMessages={ setMessages }
      onNewProject={ () => {
        /** 重置所有状态到初始值 */
        setIsGenerating(false)
        setIsThinking(false)
        setShowForm(false)
        setShowWorkflow(false)
        setUploadedImage(null)
        setFormUploadedImage(null)
        setContentDescription('')
        setFormData({
          brand: '',
          product_name: '',
          industry: '',
          competitor: '',
          product: '',
          pic: '',
        })
        setFormErrors({})
        setMessage('')
        setSelectedButton(null)
        setIsProjectExpanded(false)
      } }
    />

    {/* 主内容区 - 使用MainContent组件 */}
    <MainContent
      /** 模式控制 */
      openWorkflow={ snap.openWorkflow }
      isGenerating={ isGenerating }

      // Welcome界面配置
      selectedButton={ selectedButton }
      onButtonSelect={ setSelectedButton }

      /** 输入区域配置 */
      message={ message }
      onMessageChange={ msg => !isGenerating && setMessage(msg) }
      uploadedImage={ uploadedImage }
      onImageUpload={ (file) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          setUploadedImage(e.target?.result as string)
        }
        reader.readAsDataURL(file)
      } }
      onImageRemove={ () => setUploadedImage(null) }
      onGenerate={ () => {
        if (message.trim() && !isGenerating) {
          setContentDescription(message)
          setIsGenerating(true)
          setTimeout(() => {
            setIsThinking(true)
            setTimeout(() => {
              setIsThinking(false)
              setShowForm(true)
            }, 3000)
          }, 500)
        }
      } }

      // ChatWorkflow配置
      showWorkflow={ showWorkflow }
      showForm={ showForm }
      isThinking={ isThinking }
      thinkingExpanded={ thinkingExpanded }
      onThinkingToggle={ setThinkingExpanded }
      contentDescription={ contentDescription }
      formData={ formData }
      formErrors={ formErrors }
      formUploadedImage={ formUploadedImage }
      isSubmitting={ isSubmitting }
      onFormChange={ handleFormChange }
      onFormSubmit={ handleFormSubmit }
      onFormImageUpload={ (file) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          setFormUploadedImage(e.target?.result as string)
          handleFormChange('pic', e.target?.result as string)
        }
        reader.readAsDataURL(file)
      } }
      onFormImageRemove={ () => {
        setFormUploadedImage(null)
        handleFormChange('pic', '')
      } }

      /** 历史聊天配置 */
      messages={ messages }
      onLoadMore={ onLoadMore }
      hasMore={ hasMore }
      showStep={ snap.showStep }
      enableInput={ snap.enableInput }
      sendButtonText={ t('layout.send') }
    />
  </div>
})
