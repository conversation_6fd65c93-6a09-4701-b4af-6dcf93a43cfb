import type { IText } from 'fabric/*'
import type { TeamMember } from './components/Team'
import type { ShowMode } from './store'
import type { Lang } from './types'
import { SubmitEnum } from '@/api/ChatApi'
import { IS_ZH } from '@/config'
import { EventBus } from '@jl-org/tool'

export const editorWidth = 1024
export const editorHeight = 1024

export const showEditorWidth = 800
export const showEditorHeight = 800

export const editorMultiplier = editorWidth / showEditorWidth

export const ChatEventBus = new EventBus<
  | 'reloadSidebar'
  | 'changeChatResp'
  | 'scrollToBottom'
  | 'resetSwitch'
  | 'fillSwitch'
  | 'showImagePreview'
>({ triggerBefore: true })

/**
 * 生图数量
 */
export const genImgSize = 3

export const posterLayout: {
  title: ConstructorParameters<typeof IText>[1]
  subTitle: ConstructorParameters<typeof IText>[1]
}[] = [
  {
    title: {
      left: 16,
      top: 16,
      fill: '#224178',
      fontWeight: 700,
      fontSize: 40,
    },
    subTitle: {
      left: 16,
      top: 16 * 10,
      fontSize: 20,
    },
  },
]

export const options = [
  {
    label: 'Amazon Listing',
    value: 'awsShop',
  },
  {
    label: 'Shopee Listing',
    value: 'Shopee Listing',
    disabled: true,
  },
  {
    label: 'Individual Store Listing',
    value: 'Individual Store Listing',
    disabled: true,
  },
] as const

export function titleMap(lang?: Lang): Record<ShowMode, string> {
  if (!lang) {
    lang = IS_ZH
      ? 'zh'
      : 'en'
  }

  const data = {
    zh: {
      'img': '图片',
      'copyWrite': '策略',
      'report': '报告',
      '3dModel': '3D 模型',
      'video': '视频',
    },
    en: {
      'img': 'Image',
      'copyWrite': 'Strategy',
      'report': 'Insight',
      '3dModel': '3D Model',
      'video': 'Video',
    },
  }

  return data[lang]
}

export const descMap: Record<ShowMode, string> = {
  'img': 'Visual Architect',
  'copyWrite': 'Content Strategist',
  'report': 'Insights Analyst',
  '3dModel': 'Senior Modeler',
  'video': 'Video Director',
}

export const AdditionalContentHeight = 50
export const productWidth = 720

export const priceId = '__internal_pricePie'
export const ageId = '__internal_agePie'
export const regionId = '__internal_regionPie'

export const costMap: Record<ShowMode, number> = {
  'img': 4,
  'copyWrite': 1,
  'report': 1,
  '3dModel': 5,
  'video': 4,
}

export const showModeSortMap: Record<ShowMode, number> = {
  'copyWrite': 1,
  'img': 2,
  'video': 3,
  'report': 4,
  '3dModel': 5,
}

export const costEnumMap: Record<ShowMode | 'changeImage', SubmitEnum> = {
  'img': SubmitEnum.Image,
  'copyWrite': SubmitEnum.Copyright,
  'report': SubmitEnum.Report,
  '3dModel': SubmitEnum.Model,
  'video': SubmitEnum.Video,
  'changeImage': SubmitEnum.ChangeImage,
}

export function thinkTitleMap(done?: boolean): Record<ShowMode, {
  title: string
  part: string
}> {
  if (IS_ZH) {
    const endStr = done
      ? '分析完成'
      : '分析中'

    const data = {
      'img': {
        part: `图片 ${endStr}`,
        title: 'Marketing Image',
      },
      'copyWrite': {
        part: `策略 ${endStr}`,
        title: 'Marketing Copy',
      },
      'report': {
        part: `报告 ${endStr}`,
        title: 'Marketing Report',
      },
      '3dModel': {
        part: `3D 模型 ${endStr}`,
        title: 'Marketing 3D Model',
      },
      'video': {
        part: `视频 ${endStr}`,
        title: 'Marketing Video',
      },
    }

    return data
  }

  const endStr = done
    ? 'Analyze done'
    : 'Analyzing'

  const data = {
    'img': {
      part: `Image ${endStr}`,
      title: 'Marketing Image',
    },
    'copyWrite': {
      part: `Strategy ${endStr}`,
      title: 'Marketing Copy',
    },
    'report': {
      part: `Report ${endStr}`,
      title: 'Marketing Report',
    },
    '3dModel': {
      part: `3D Model ${endStr}`,
      title: 'Marketing 3D Model',
    },
    'video': {
      part: `Video ${endStr}`,
      title: 'Marketing Video',
    },
  }

  return data
}

export const pieWidth = 550
export const pieHeight = 400

export const avatarMap: Record<TeamMember['name'], { url: string, desc: string }> = {
  Mary: {
    url: new URL('./assets/Mary.webp', import.meta.url).href,
    desc: 'Marketing Dept',
  },
  Bob: {
    url: new URL('./assets/Bob.webp', import.meta.url).href,
    desc: 'Branding Dept',
  },
  David: {
    url: new URL('./assets/David.webp', import.meta.url).href,
    desc: 'Designing Dept',
  },
}
