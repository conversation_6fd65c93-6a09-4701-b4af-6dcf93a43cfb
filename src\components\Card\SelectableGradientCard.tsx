import { cn } from '@/utils'
import { memo, useState, useEffect, ReactNode } from 'react'

/**
 * 可选中的渐变边框卡片组件
 * 特性：
 * 1. 渐变边框始终显示
 * 2. 悬浮时显示背景
 * 3. 支持单选模式（通过父组件管理）
 * 4. 内容区域支持悬浮和选中背景效果
 */
export const SelectableGradientCard = memo((props: SelectableGradientCardProps) => {
  const {
    className,
    children,
    borderWidth = 1.5,
    borderGradient = 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
    hoverBackground = 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)',
    selectedBackground = 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)',
    selected = false,
    onSelectedChange,
    renderContent,
    contentClassName,
    ...rest
  } = props
  
  const [isHovered, setIsHovered] = useState(false)
  const [isSelected, setIsSelected] = useState(selected)

  useEffect(() => {
    setIsSelected(selected)
  }, [selected])

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const newSelected = !isSelected
    setIsSelected(newSelected)
    onSelectedChange?.(newSelected)
    rest.onClick?.(e)
  }

  // 内容背景样式
  const contentBackground = (isHovered || isSelected) 
    ? (isSelected ? selectedBackground : hoverBackground)
    : 'transparent'

  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-2xl transition-all duration-300',
        className
      )}
      style={{
        border: `${borderWidth}px solid transparent`,
        backgroundClip: 'padding-box, border-box',
        backgroundOrigin: 'padding-box, border-box',
        backgroundImage: `linear-gradient(to right, #fff, #fff), ${borderGradient}`,
      }}
      onClick={handleClick}
      {...rest}
    >
      {/* 如果提供了 renderContent，使用自定义渲染 */}
      {renderContent ? (
        renderContent({
          isHovered,
          isSelected,
          contentBackground,
          onMouseEnter: () => setIsHovered(true),
          onMouseLeave: () => setIsHovered(false),
        })
      ) : (
        /* 默认内容包装 */
        <div
          className={cn('transition-all duration-300', contentClassName)}
          style={{ background: contentBackground }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {children}
        </div>
      )}
    </div>
  )
})

SelectableGradientCard.displayName = 'SelectableGradientCard'

export type SelectableGradientCardProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
  children?: React.ReactNode
  borderWidth?: number
  borderGradient?: string
  hoverBackground?: string
  selectedBackground?: string
  selected?: boolean
  onSelectedChange?: (selected: boolean) => void
  /** 自定义内容渲染函数 */
  renderContent?: (props: {
    isHovered: boolean
    isSelected: boolean
    contentBackground: string
    onMouseEnter: () => void
    onMouseLeave: () => void
  }) => ReactNode
  /** 内容容器的样式类名 */
  contentClassName?: string
}