import type { Message } from '@/api/GrokApi'
import type { FileItem } from '@/components/Uploader'
import type { TeamMember } from './components/Team'
import type { ShowMode } from './store'
import type { Lang, Msg } from './types'
import { AiChatTaskStatus, batchReplaceImg, ChatApi, IsShow, processImg, RecordRole, SessionType, SubmitEnum } from '@/api/ChatApi'
import { ChatGPTApi, Role } from '@/api/ChatGPTApi'
import { CozeApi } from '@/api/CozeApi'
import { DeepSeekApi } from '@/api/DeepSeekApi'
import { FileAPI } from '@/api/FileAPI'
import { GrokApi } from '@/api/GrokApi'
import { Loading } from '@/components/Loading'
import { ZhBg } from '@/components/ZhBg'
import { god } from '@/god'
import { useMemoFn, useT } from '@/hooks'
import { updateUserInfo } from '@/store/userStore'
import { addTimestampParam, createPublishTagEl, getImgNetUrl } from '@/utils'
import { blobToBase64, genArr, Reg, urlToBlob } from '@jl-org/tool'
import { motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'
import { Chat } from './components/Chat'
import { ImagePreviewCard } from './components/ImagePreviewCard'
import { ChatEventBus, costEnumMap, editorHeight, editorWidth, genImgSize, thinkTitleMap } from './constants'
import { createImgDetailLayout, genDetailEl, htmlToImg } from './genDetailImg'
import { getAdvantage, getDetailPosition, getScene } from './getJsonPrompt'
import { get3DModelAnalzing, getVideoAnalyzing } from './mockData'
import { genVidoePrompt, marketResearchPrompt, posterImg, productDesc } from './prompt'
import { chatStore } from './store'
import { genMsg, type } from './tool'
import { useChatData } from './useChatData'
import '@/styles/css/github-light.css'

function App() {
  const [showImagePreview, setShowImagePreview] = useState(false)
  const t = useT()
  const {
    snap,
    isAWS,
    messages,
    isLoading,

    setMessages,
    setIsLoading,

    sendMsg,
    sendTink,
    updateThink,
    thinkDone,
    thinkData,
    sendStepThink,
    updateMsgById,

    loadingAnswer,
    updateAnswer,
    onFail,

    countdown,
    disableCountdown,

    handleSendMessage,
  } = useChatData()

  const lang = useRef<Lang>('en')
  const DavidCompelteArr = useRef<string[]>([])

  /** 监听来自 ChatWorkflow 的预览事件 */
  useEffect(() => {
    const handleShowPreview = () => setShowImagePreview(true)
    ChatEventBus.on('showImagePreview', handleShowPreview)
    return () => {
      ChatEventBus.off('showImagePreview', handleShowPreview)
    }
  }, [])

  useEffect(
    () => {
      // @ts-ignore
      window.test = () => {
        createPublishTagEl(
          'PhotoG AI Chat',
          'PhotoG AI Chat is a chatbot that helps you generate images and videos based on your input.',
          ['data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAQCAYAAAAWGF8bAAAAAXNSR0IArs4c6QAAAT5JREFUOE9jjNELahDUFqtnoBC8v/KSgYGRsZExRjfwv/O6GAqNg2g/V7ebgTE3MuO/UZMr3MD3V18yCGqLk2UBhoEgAZG7Hxluf//BYNTsSrLBKAaCXCbRf5rBS1AAbOBmWzEGxXA9DJeC1IEANl9guPBR9BoGVU4OsIYbrnIYBoIMO1e7m0FDnY+BK8Ycw1AMA0EaPlx5ySCgI46hGGZYZZkW2MD2rmsYhmKNFGyxgW4YTA26oTgNvL/yElgPKAxxGYbNUKwGggzT//EOrP7A3d8MoAQL8yY212/Y9IThoYkmOIgwDIQZFuAnA4mYm5/A4YUP4DQQ5DX5M9cZYIYRm7LxGvhtyUkGTQ38LkK36PqNT/DYxppsiHUZsjpYIic62RBryd6gJQyMoOKL4f//ekEd8goEZMveX33VCACuoMnrHqg3dQAAAABJRU5ErkJggg=='],
        )
      }
    },
    [],
  )

  const previewIndex = useRef(0)
  const updatePreviewMsg = useMemoFn((msg: Msg, switchMode = true) => {
    switchMode && (chatStore.showMode = 'img')
    chatStore.selectedMsg = msg
    chatStore.previewMsgs[previewIndex.current++] = msg
  })

  const onMsg = async (content: string, files: FileItem[]) => {
    return isAWS
      ? handleAWSGenerateImg(content, files)
      : handleSendMessage(content, files)
  }

  /**
   * 电商生图
   */
  const handleAWSGenerateImg = async (content: string, files: FileItem[]) => {
    if (!chatStore.selectedMode.length) {
      return god.messageWarn('Please select an output')
    }

    const file = files[0]
    if (!file) {
      return god.messageWarn('Please upload an image')
    }

    chatStore.loading = true
    DavidCompelteArr.current = []
    lang.current = Reg.chinese.test(content)
      ? 'zh'
      : 'en'

    setIsLoading(true)
    previewIndex.current = 0
    chatStore.openWorkflow = true
    chatStore.showStep = false
    chatStore.chatId = ''
    chatStore.activeTeamMembers = ['Mary']
    chatStore.currentTeamMember = 'Mary'

    const size = getImgSize()

    const session = await ChatApi.createSession()
    chatStore.chatId = session.chatId
    await ChatApi.submitTask(session.chatId, chatStore.selectedMode.map(item => costEnumMap[item]))
      .then(() => updateUserInfo())
      .finally(() => chatStore.loading = false)

    sendMsg(
      {
        content,
        mode: 'awsShop',
      },
      files.map(item => ({
        base64: item.base64,
        rawUrl: item.base64,
      })),
    )

    try {
      sendStepThink([])
      const { url } = await FileAPI.upSingleBase64(file.base64)
        .catch(() => {
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Image,
            status: AiChatTaskStatus.Failed,
          }])
          throw new Error('Failed to upload image')
        })

      ChatApi.recordSession({
        chatId: chatStore.chatId,
        aiChatDataList: [{
          content,
          originalImageUrls: [url],
          isShow: IsShow.Show,
          role: RecordRole.User,
          type: SessionType.Chat,
        }],
      }).then(() => ChatEventBus.emit('reloadSidebar'))

      const [cutoutImg, imgDesc] = await cutoutImgAndPreview(url)
        .catch(() => {
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Image,
            status: AiChatTaskStatus.Failed,
          }])
          throw new Error('Failed to process image')
        })

      /** 测试数据 */
      // const cutoutImg = file.base64
      // const imgDesc = { text: 'A beautiful girl' }

      setMessages(prev => prev.filter(item => item.type !== 'step'))

      await marketResearch(imgDesc.text)

      await genCopyWrite(imgDesc.text)

      genModel(cutoutImg)

      /**
       * coze 生图和视频
       */
      // const { scenes } = await genScene(cutoutImg, size)
      // const { urls, md, video } = await genImgCoze(scenes.scene, cutoutImg)
      // await genVideo(urls[0], video || imgDesc.text)

      /**
       * ChatGPT 生图和视频
       */
      const {
        bgUrls,
        scenes,
      } = await genImgAndScenes(cutoutImg, size)

      console.log({ bgUrls, cutoutImg })
      if (bgUrls[0]) {
        genImgAndVideo(bgUrls, cutoutImg, imgDesc.text)
      }

      createPublishTagEl(
        'By PhotoG',
        `${chatStore.report}\n ${chatStore.copyWrite} \n`,
        bgUrls,
      )
    }
    catch (error: any) {
      god.messageError('Failed to process image')
      console.log(error)
      onFail(error?.message)

      // Object.entries(chatStore.stepStatus).forEach(([k, v]) => {
      //   if (v === 'finish')
      //     return
      //   chatStore.stepStatus[k as ShowMode] = 'error'
      // })
    }
    finally {
      god.messageDismiss()
      setIsLoading(false)
    }
  }

  /***************************************************
   *                    Fns
  ***************************************************/
  async function genScene(cutoutImg: string, size: number) {
    const { promise, resolve, reject } = Promise.withResolvers<{
      scenes: { scene: string[] }
    }>()

    let scenes = { scene: [] as string[] }

    /**
     * 四张场景或者指定张场景
     */
    if (chatStore.selectedMode.includes('img') || chatStore.selectedMode.includes('video')) {
      if (chatStore.selectedMode.includes('img')) {
        chatStore.showMode = 'img'
      }
      addDavid()

      try {
        scenes = await getScene(cutoutImg!, size)
      }
      catch (error) {
        chatStore.stepStatus.img = 'error'
        reject()
      }
    }

    resolve({
      scenes,
    })

    return promise
  }

  async function genImgCoze(scenes: string[], cutoutImg: string) {
    addDavid()
    const urls: string[] = []
    let veo = ''
    let md = ''

    if (chatStore.selectedMode.includes('img')) {
      for (const scene of scenes) {
        const thinkId = sendTink()
        const info = await CozeApi.streamGenImage(
          {
            prompt: `This is my scene: ${scene}.`,
            imageUrl: cutoutImg,
          },
          async (data) => {
            updateThink(data.content, thinkId)
          },
        )
        await thinkDone(thinkId)

        /***************************************************
         *                  获取图片
         ***************************************************/

        await CozeApi.streamResumeGenImage(
          {
            eventId: info.id,
            interruptType: info.type,
            resumeData: 'resume',
          },
          async (data) => {
            if (data.finalImgData) {
              const { images, video, markdown } = data.finalImgData.content
              urls.push(...images)
              veo = video && video
              md = markdown && markdown

              markdown && ChatApi.recordSession({
                chatId: chatStore.chatId,
                aiChatDataList: [{
                  content: '',
                  reasoningContent: markdown,
                  isShow: IsShow.Show,
                  role: RecordRole.Ai,
                  type: SessionType.Image,
                }],
              })

              for (const item of images) {
                const msg = genMsg(
                  { content: '', type: 'answer' },
                  [{
                    base64: item,
                    rawUrl: item,
                  }],
                )
                updatePreviewMsg(msg)

                ChatApi.recordSession({
                  chatId: chatStore.chatId,
                  aiChatDataList: [{
                    content: '',
                    originalImageUrls: [await getImgNetUrl(item)],
                    isShow: IsShow.Show,
                    role: RecordRole.Ai,
                    type: SessionType.Image,
                  }],
                })
              }
            }
          },
        )
      }
    }

    checkDavidCompleted('img')
    return {
      urls,
      video: veo,
      md,
    }
  }

  async function genImgAndVideo(bgUrls: string[], cutoutImg: string, imgDesc: string) {
    addDavid()
    const replaceUrls = await batchReplaceImg(bgUrls, cutoutImg)

    genVideo(replaceUrls[0], imgDesc || '')

    if (chatStore.selectedMode.includes('img')) {
      replaceUrls.forEach(async (item) => {
        const msg = genMsg(
          { content: '', type: 'answer', currentTeamMember: chatStore.currentTeamMember },
          [{
            base64: item,
            rawUrl: item,
          }],
        )
        chatStore.showMode = 'img'
        updatePreviewMsg(msg)

        ChatApi.recordSession({
          chatId: chatStore.chatId,
          aiChatDataList: [{
            content: '',
            originalImageUrls: [await getImgNetUrl(item)],
            isShow: IsShow.Show,
            role: RecordRole.Ai,
            type: SessionType.Image,
          }],
        })
      })

      Promise.all([
        ChatGPTApi.genImg({
          prompt: posterImg(lang.current),
          imageUrl: replaceUrls.at(-1)!,
        }).then(async (posterImgs) => {
          const posterImg = posterImgs[0]
          const msg = genMsg(
            {
              content: '',
              type: 'answer',
              currentTeamMember: chatStore.currentTeamMember,
            },
            [{
              base64: posterImg,
              rawUrl: addTimestampParam(replaceUrls.at(-1)!),
            }],
          )

          updatePreviewMsg(msg)
          ChatApi.recordSession({
            chatId: chatStore.chatId,
            aiChatDataList: [{
              content: '',
              composeImageUrls: [await getImgNetUrl(posterImg)],
              originalImageUrls: [await getImgNetUrl(replaceUrls.at(-1)!)],
              isShow: IsShow.Show,
              role: RecordRole.Ai,
              type: SessionType.Image,
            }],
          })
        }),
      ])
        .then(() => {
          chatStore.stepStatus.img = 'finish'
          chatStore.showMode = 'img'
          checkDavidCompleted('img')

          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Image,
            status: AiChatTaskStatus.Finished,
          }])
        })
        .catch(() => {
          chatStore.stepStatus.img = 'error'

          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Image,
            status: AiChatTaskStatus.Failed,
          }])
        })
    }
  }

  async function genCopyWrite(imgDesc: string) {
    if (chatStore.selectedMode.includes('copyWrite')) {
      chatStore.stepStatus.copyWrite = 'process'
      chatStore.activeTeamMembers.push('Bob')
      chatStore.currentTeamMember = 'Bob'

      const { promise, dispose } = stepChatWithGrok(
        content => chatStore.copyWrite = content,
        productDesc(lang.current) + imgDesc,
      )
      try {
        const data = await promise
        disableCountdown()
        const { content, reason } = data

        ChatApi.confirmTask(chatStore.chatId, [{
          type: SubmitEnum.Copyright,
          status: AiChatTaskStatus.Finished,
        }])

        ChatApi.recordSession({
          chatId: chatStore.chatId,
          aiChatDataList: [{
            content: '',
            grokReason: GrokApi.splitReason(reason),
            isShow: IsShow.Show,
            role: RecordRole.Ai,
            type: SessionType.Copyright,
          }],
        })

        chatStore.copyWrite = content
        ChatApi.recordSession({
          chatId: chatStore.chatId,
          aiChatDataList: [{
            content,
            isShow: IsShow.Show,
            role: RecordRole.Ai,
            type: SessionType.Copyright,
          }],
        })
        chatStore.stepStatus.copyWrite = 'finish'
        chatStore.showMode = 'copyWrite'

        completeExpertTask('Bob')
      }
      catch (error) {
        chatStore.stepStatus.copyWrite = 'error'
        ChatApi.confirmTask(chatStore.chatId, [{
          type: SubmitEnum.Copyright,
          status: AiChatTaskStatus.Failed,
        }])
      }
      finally {
        dispose()
      }
    }
  }

  function getImgSize() {
    const size = !chatStore.selectedMode.includes('img') && chatStore.selectedMode.includes('video')
      ? 1
      : genImgSize
    chatStore.previewMsgs = genArr<Msg>(size + 2, () => genMsg({ currentTeamMember: chatStore.currentTeamMember }))
    return size
  }

  async function genImgAndScenes(cutoutImg: string, size: number) {
    const { promise, resolve, reject } = Promise.withResolvers<{
      bgUrls: string[]
      scenes: { scene: string[] }
    }>()

    let scenes = { scene: [] as string[] }

    let scenesPromise = [{ imgUrls: [] as string[] }]
    if (chatStore.selectedMode.includes('img') || chatStore.selectedMode.includes('video')) {
      try {
        addDavid()
        chatStore.showMode = 'img'
        scenes = await getScene(cutoutImg!, size)
        scenesPromise = await Promise.all(scenes.scene.map(scene => ChatGPTApi.genImg({
          prompt: `The image resolution must be ${editorWidth} * ${editorHeight}. Please Place my product map in the scenario to generate a marketing map. Here is my scene: ${scene}`,
          imageUrl: cutoutImg!,
        }).then(res => res[0]),
        )).then(res => res.filter(Boolean)).then((res) => {
          return res.map(item => ({
            imgUrls: [item],
          }))
        })
      }
      catch (error) {
        chatStore.stepStatus.img = 'error'
        reject()
      }
    }

    const bgUrls: string[] = ['']
    scenesPromise.forEach((item, i) => {
      bgUrls[i] = item.imgUrls[0]
    })

    resolve({
      bgUrls,
      scenes,
    })

    return promise
  }

  function getThinkTop(showMode: ShowMode, done?: boolean) {
    const data = thinkTitleMap(done)[showMode]
    return {
      thinkTitle: `${data.title}`,
      thinkingText: `${data.part}`,
    }
  }

  async function genVideo(imgUrl: string, desc: string) {
    if (!chatStore.selectedMode.includes('video')) {
      return
    }

    if (!imgUrl) {
      throw new Error('image is load fail')
    }

    chatStore.stepStatus.video = 'process'
    addDavid()

    try {
      const videoThinkId = sendTink({
        mode: 'awsShop',
        ...getThinkTop('video'),
      })
      const videoThink = getVideoAnalyzing(lang.current)
      updateThink(videoThink.prev, videoThinkId)

      const { content, reason } = await DeepSeekApi.deepSeekThinkWithStream({
        content: genVidoePrompt(lang.current, desc),
        onMsg: ({ reason }) => {
          updateThink(videoThink.all(reason), videoThinkId)
        },
      })
      thinkDone(videoThinkId, getThinkTop('video', true))

      ChatApi.recordSession({
        chatId: chatStore.chatId,
        aiChatDataList: [{
          content: '',
          reasoningContent: videoThink.all(reason),
          isShow: IsShow.Show,
          role: RecordRole.Ai,
          type: SessionType.Video,
        }],
      })

      await ChatApi.pollingVideo({
        duration: '5',
        resourceVideoGenerationTypeEnum: 'IMAGE_TO_VIDEO',
        aspectRatio: '16:9',
        imageUrl: imgUrl,
        prompt: content,
      })
        .then(async (res) => {
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Video,
            status: AiChatTaskStatus.Finished,
          }])

          chatStore.videoUrl = res[0]
          chatStore.stepStatus.video = 'finish'
          chatStore.showMode = 'video'

          ChatApi.recordSession({
            chatId: chatStore.chatId,
            aiChatDataList: [{
              content: '',
              videoUrls: [res[0]],
              isShow: IsShow.Show,
              role: RecordRole.Ai,
              type: SessionType.Video,
            }],
          })
        })
        .catch(() => {
          chatStore.stepStatus.video = 'error'
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Video,
            status: AiChatTaskStatus.Failed,
          }])
        })
    }
    catch (error) {
      chatStore.stepStatus.video = 'error'
    }
  }

  async function genVideoWithGrok(imgUrl: string, desc: string) {
    if (!chatStore.selectedMode.includes('video')) {
      return
    }

    chatStore.stepStatus.video = 'process'
    addDavid()

    try {
      let id = sendStepThink([])
      const { content, reason } = await GrokApi.streamGrok({
        content: genVidoePrompt(lang.current, desc),
        onMsg: ({ reason }) => {
          id = sendStepThink(normalizeStepData(reason), id)
        },
      })
      sendStepThink(normalizeStepData(reason), id, { thinkStepDone: true })

      ChatApi.recordSession({
        chatId: chatStore.chatId,
        aiChatDataList: [{
          content: '',
          grokReason: GrokApi.splitReason(reason),
          isShow: IsShow.Show,
          role: RecordRole.Ai,
          type: SessionType.Video,
        }],
      })

      await ChatApi.pollingVideo({
        duration: '5',
        resourceVideoGenerationTypeEnum: 'IMAGE_TO_VIDEO',
        aspectRatio: '16:9',
        imageUrl: imgUrl,
        prompt: content,
      })
        .then(async (res) => {
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Video,
            status: AiChatTaskStatus.Finished,
          }])

          chatStore.videoUrl = res[0]
          chatStore.stepStatus.video = 'finish'
          chatStore.showMode = 'video'
          checkDavidCompleted('video')

          ChatApi.recordSession({
            chatId: chatStore.chatId,
            aiChatDataList: [{
              content: '',
              videoUrls: [res[0]],
              isShow: IsShow.Show,
              role: RecordRole.Ai,
              type: SessionType.Video,
            }],
          })
        })
        .catch(() => {
          chatStore.stepStatus.video = 'error'
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Video,
            status: AiChatTaskStatus.Failed,
          }])
        })
    }
    catch (error) {
      chatStore.stepStatus.video = 'error'
    }
  }

  async function genModel(imgUrl: string) {
    if (!chatStore.selectedMode.includes('3dModel')) {
      return
    }

    chatStore.stepStatus['3dModel'] = 'process'
    addDavid()
    try {
      ChatApi.pollingModel({
        imgUrl,
      })
        .then(async (res) => {
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Model,
            status: AiChatTaskStatus.Finished,
          }])

          chatStore.modelUrl = res.modelUrl
          chatStore.stepStatus['3dModel'] = 'finish'
          chatStore.showMode = '3dModel'
          checkDavidCompleted('3dModel')

          await ChatApi.recordSession({
            chatId: chatStore.chatId,
            aiChatDataList: [{
              content: '',
              modelUrls: [res.modelUrl],
              isShow: IsShow.Show,
              role: RecordRole.Ai,
              type: SessionType.Model,
            }],
          })
        })
        .catch(() => {
          chatStore.stepStatus['3dModel'] = 'error'
          ChatApi.confirmTask(chatStore.chatId, [{
            type: SubmitEnum.Model,
            status: AiChatTaskStatus.Failed,
          }])
        })

      ChatApi.recordSession({
        chatId: chatStore.chatId,
        aiChatDataList: [{
          content: '',
          isShow: IsShow.Show,
          role: RecordRole.Ai,
          type: SessionType.Model,
        }],
      })
    }
    catch (error) {
      chatStore.stepStatus['3dModel'] = 'error'
    }
  }

  function stepChatWithGrok(
    cb: (content: string) => void,
    firstContent: string,
    firstsImgUrl?: string,
  ) {
    let isFirst = true
    const history: Message[] = []

    const { promise, resolve } = Promise.withResolvers<{
      content: string
      reason: string
    }>()

    function addHistory(text: string, aiResp: string, imgUrl?: string) {
      history.push({
        role: Role.User,
        content: [{
          type: 'text',
          text,
        }],
      })
      if (imgUrl) {
        history.at(-1)?.content.push({
          type: 'image_url',
          image_url: {
            url: imgUrl,
          },
        })
      }

      history.push({
        role: Role.Assistant,
        content: [{
          type: 'text',
          text: aiResp,
        }],
      })
    }

    async function updateStep(text?: string, imgUrl?: string) {
      let stepId = sendStepThink([])

      if (isFirst) {
        const { content, reason } = await GrokApi.streamGrok({
          content: firstContent,
          imgUrl: firstsImgUrl,
          history,
          onMsg: ({ reason }) => {
            stepId = sendStepThink(normalizeStepData(reason), stepId)
          },
        })

        chatStore.enableInput = true
        cb(content)
        sendStepThink(normalizeStepData(reason), stepId, { thinkStepDone: true })
        addHistory(firstContent, content, firstsImgUrl)

        isFirst = false

        return {
          content,
          reason,
        }
      }
      else {
        if (!text) {
          return {
            content: '',
            reason: '',
          }
        }

        const { content, reason } = await GrokApi.streamGrok({
          content: text,
          imgUrl,
          history,
          onMsg: ({ reason }) => {
            stepId = sendStepThink(normalizeStepData(reason), stepId)
          },
        })

        chatStore.enableInput = true
        cb(content)
        sendStepThink(normalizeStepData(reason), stepId, { thinkStepDone: true })
        addHistory(text, content, imgUrl)

        return {
          content,
          reason,
        }
      }
    }

    async function loopConfirm() {
      const data = await updateStep()
      await countdown(t('chat.report-confirm')).then(() => resolve(data))

      return data
    }

    ChatEventBus.on('changeChatResp', async (msg: string, files: FileItem[]) => {
      if (!msg) {
        god.messageWarn('Please enter your answer')
        return
      }

      const base64 = files[0]?.base64
      chatStore.enableInput = false

      sendMsg(
        { content: msg },
        files,
      )

      const data = await updateStep(msg, base64)
      await countdown(t('chat.report-confirm')).then(() => resolve(data))
    })

    loopConfirm()

    return {
      promise,
      dispose: () => {
        ChatEventBus.off('changeChatResp')
      },
    }
  }

  async function marketResearch(desc: string) {
    if (!chatStore.selectedMode.includes('report')) {
      return ''
    }

    chatStore.stepStatus.report = 'process'
    const { promise, dispose } = stepChatWithGrok(
      content => chatStore.report = content,
      marketResearchPrompt(lang.current) + desc,
    )

    try {
      const data = await promise
      disableCountdown()
      const { content, reason } = data

      /** 确认扣分 */
      ChatApi.confirmTask(chatStore.chatId, [{
        type: SubmitEnum.Report,
        status: AiChatTaskStatus.Finished,
      }])

      chatStore.report = content
      chatStore.stepStatus.report = 'finish'
      completeExpertTask('Mary')

      ChatApi.recordSession({
        chatId: chatStore.chatId,
        aiChatDataList: [{
          content,
          isShow: IsShow.Show,
          role: RecordRole.Ai,
          type: SessionType.Report,
        }],
      })

      ChatApi.recordSession({
        chatId: chatStore.chatId,
        aiChatDataList: [{
          content: '',
          grokReason: GrokApi.splitReason(reason),
          isShow: IsShow.Show,
          role: RecordRole.Ai,
          type: SessionType.Report,
        }],
      })

      return content
    }
    catch (error) {
      ChatApi.confirmTask(chatStore.chatId, [{
        type: SubmitEnum.Report,
        status: AiChatTaskStatus.Failed,
      }])

      return Promise.reject(error)
    }
    finally {
      dispose()
    }
  }

  async function cutoutImgAndPreview(url: string) {
    const imgPromise = (async function () {
      if (chatStore.selectedMode.includes('img')) {
        chatStore.stepStatus.img = 'process'
      }

      return processImg(url)
    })()

    return Promise.all([
      imgPromise.then(async (cutoutImg) => {
        if (!cutoutImg) {
          throw new Error('Image matting fail')
        }

        const msg = genMsg(
          {
            mode: 'awsShop',
            content: 'Image matting',
            type: 'answer',
            currentTeamMember: chatStore.currentTeamMember,
          },
          [{
            base64: cutoutImg,
            rawUrl: cutoutImg,
          }],
        )

        updatePreviewMsg(msg, false)
        ChatApi.recordSession({
          chatId: chatStore.chatId,
          aiChatDataList: [{
            content: '',
            originalImageUrls: [await getImgNetUrl(cutoutImg)],
            isShow: IsShow.Show,
            role: RecordRole.Ai,
            type: SessionType.Image,
          }],
        })
        // .then(() => ChatEventBus.emit('reloadSidebar'))

        return cutoutImg
      }),
      ChatApi.imgRecognition(url),
    ])
  }

  function checkDavidCompleted(taskType: ShowMode) {
    DavidCompelteArr.current.push(taskType)
    const count = chatStore.selectedMode.length - 2

    if (DavidCompelteArr.current.length >= count) {
      completeExpertTask('David')
    }
  }

  function addDavid() {
    chatStore.activeTeamMembers = [...new Set([...chatStore.activeTeamMembers, 'David'])] as TeamMember['name'][]
    chatStore.currentTeamMember = 'David'
  }

  function completeExpertTask(name: TeamMember['name']) {
    chatStore.completeTeamMembers.push(name)
    chatStore.currentTeamMember = name
    chatStore.activeTeamMembers = chatStore.activeTeamMembers.filter(item => item !== name)
  }

  function normalizeStepData(reason: string) {
    return GrokApi.splitReason(reason).map(item => ({
      ...item,
      thinkDoneText: item.title,
      thinkingText: item.title,
    }))
  }

  async function genFeatureImg(imgUrl: string) {
    const url = addTimestampParam(imgUrl)
    const blob = await urlToBlob(url)
    const base64 = await blobToBase64(blob)

    const [advantage, position] = await Promise.all([
      getAdvantage(base64),
      getDetailPosition(base64),
    ])

    const detailEls = position.map(item => genDetailEl(base64, item))
    const detailImgs = await Promise.all(detailEls.map(item => htmlToImg(item)))

    const imgEl = createImgDetailLayout(
      advantage.map((item, i) => ({
        ...item,
        url: detailImgs[i],
      })),
      false,
    )
    const imgWithTxtEl = createImgDetailLayout(
      advantage.map((item, i) => ({
        ...item,
        url: detailImgs[i],
      })),
    )

    const img = await htmlToImg(imgEl)
    const imgWithTxt = await htmlToImg(imgWithTxtEl)

    return {
      img,
      imgWithTxt,
      advantage,
      position,
    }
  }

  /***************************************************
   *                    测试数据
   ***************************************************/
  // useAsyncEffect(testData, [])
  async function testData() {
    chatStore.openWorkflow = true
    // genImgCoze(['一个美女坐在椅子上'], 'http://chengguo-public.oss-cn-shanghai.aliyuncs.com/7653ce6a-849b-4a10-b156-d681681f80d0_cdvA_ZniXFzjc2bxlzIE.png?Expires=1747395878&OSSAccessKeyId=LTAI5tDMdaaXonvUs5wVMVZS&Signature=sTAB0Q9x8mZ7xNQ1M8%2Fwiy9N6GE%3D')
    //   .then(res => console.log(res))

    sendStepThink([
      { markdown: '1. 生成场景图', thinkDoneText: 'NmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNmNm' },
      { markdown: '2. 生成图片', thinkDoneText: 'Nm' },
      { markdown: '3. 生成视频', thinkDoneText: 'Nm' },
      { markdown: '4. 生成3D模型', thinkDoneText: 'Nm' },
    ])

    countdown(t('chat.report-confirm').repeat(5))
    countdown(t('chat.report-confirm').repeat(5))

    setTimeout(() => {
      disableCountdown()
      chatStore.enableInput = false
      chatStore.activeTeamMembers.push('Bob', 'David')
      chatStore.currentTeamMember = 'Bob'
    }, 2000)
    return

    setIsLoading(true)
    sendMsg({
      content: 'Generate Amazon listing images for my chair',
    })
    chatStore.stepStatus.img = 'process'
    const cutoutImgMsg: Msg = {
      ...genMsg({ currentTeamMember: chatStore.currentTeamMember }),
      content: 'Generate Amazon listing images for my chair',
      mode: 'awsShop',
      type: 'answer',
      files: [{
        base64: new URL('@/refactor/Photog/LeftSider/assets/try3.png', import.meta.url).href,
        rawUrl: new URL('@/refactor/Photog/LeftSider/assets/try3.png', import.meta.url).href,
        file: new File([], ''),
      }],
    }
    chatStore.selectedMsg = cutoutImgMsg
    chatStore.previewMsgs[0] = cutoutImgMsg

    const data = get3DModelAnalzing(lang.current)
    const id2 = loadingAnswer()
    await type(data.all(), txt => updateAnswer(id2, txt), 0)
  }

  return (
    <div className="relative size-full flex gap-4">
      {/* 左侧主内容区域 - 可被挤压 */}
      <motion.div
        className="relative overflow-hidden rounded-lg bg-white"
        animate={ {
          width: showImagePreview
            ? 'calc(50% - 8px)'
            : '100%',
        } }
        transition={ { type: 'spring', damping: 25, stiffness: 300 } }
      >
        <ZhBg>
          <Chat
            setMessages={ setMessages }
            messages={ messages }
            onSendMsg={ onMsg }
            onLoadMore={ () => { } }
            isLoading={ isLoading }
            hasMore={ false }
            className="mx-auto h-full w-full"
          />
        </ZhBg>

        <Loading loading={ snap.loading }></Loading>
      </motion.div>

      {/* 右侧图片预览卡片 */}
      <ImagePreviewCard
        isOpen={ showImagePreview }
        onClose={ () => setShowImagePreview(false) }
      />
    </div>
  )
}

export default App
