import { memo, useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'
import SvgIcon from '@/components/SvgIcon'
import cn from 'clsx'
import { ChatApi, SessionType } from '@/api/ChatApi'
import { Role } from '@/api/ChatGPTApi'
import { RomanNumMap } from '@/config'
import { god } from '@/god'
import { createPublishTagEl } from '@/utils'
import { genArr, timeGap, wait } from '@jl-org/tool'
import { motion, AnimatePresence } from 'framer-motion'
import { ChatEventBus, thinkTitleMap } from '@/pages/ChatV2/constants'
import { chatStore, resetChatStore } from '@/pages/ChatV2/store'
import { genMsg } from '@/pages/ChatV2/tool'
import type { GrokReason } from '@/api/ChatApi'
import type { ShowMode } from '@/pages/ChatV2/store'
import type { Msg } from '@/pages/ChatV2/types'

// 聊天记录类型
type ChatItem = {
  id: string
  chatId: string
  img: string
  title: string
  subtitle: string
  timestamp: string
}

// 导出类型
export type ProjectSidebarRef = {
  reload: () => void
}

const ProjectSidebar = memo(forwardRef<ProjectSidebarRef>((props, ref) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [items, setItems] = useState<ChatItem[]>([])
  const [hasMore, setHasMore] = useState(true)
  const [loading, setLoading] = useState(false)
  const [selectedId, setSelectedId] = useState('')
  const page = useRef(1)

  // 加载聊天记录
  const loadMore = useCallback(async () => {
    try {
      const data = await ChatApi.querySession({
        page: page.current++,
        size: 10,
        sort: 'createTime,desc',
      })

      setItems((prev) => {
        const newItems = data.list.map(item => ({
          id: item.id,
          chatId: item.chatId,
          img: item.originalImageUrls?.[0] || '',
          title: item.content || '无标题',
          subtitle: '',
          timestamp: timeGap(new Date(new Date(item.createTime).getTime() + 500)) || '',
        }))

        setHasMore(
          (data.total > prev.length + data.list.length) &&
          (prev.length + data.list.length) < 100
        )

        return [...prev, ...newItems]
      })
    } catch (error) {
      console.error('Failed to load chat sessions:', error)
    }
  }, [])

  // 重置聊天记录
  const resetChat = () => {
    page.current = 1
    setItems([])
    setHasMore(true)
    loadMore()
  }

  // 重新加载
  function reload() {
    page.current = 1
    setItems([])
    setHasMore(true)
    loadMore()
  }

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    reload,
  }))

  // 监听重新加载事件
  useEffect(() => {
    ChatEventBus.on('reloadSidebar', reload)
    return () => ChatEventBus.off('reloadSidebar', reload)
  }, [])

  // 点击聊天记录项 - 完整迁移ChatV2的逻辑
  const handleItemClick = async (id: string) => {
    if (loading)
      return

    const chatId = items.find(item => item.id === id)?.chatId
    if (!chatId) {
      god.messageWarn('Not Found ID')
      return
    }

    reset()
    setSelectedId(id)
    setLoading(true)
    chatStore.loading = true
    chatStore.fromHistory = true

    const data = await ChatApi.querySessionByChatId({
      chatId,
      page: 1,
      size: 999,
      sort: 'createTime,asc',
    })
      .finally(() => {
        setLoading(false)
        chatStore.loading = false
      })

    if (data.list.length <= 0) {
      god.messageWarn('No Data')
      setLoading(false)
      chatStore.loading = false
      return
    }

    let romanNum = 0
    chatStore.chatId = chatId
    chatStore.selectedMode = []

    const getThinkTop = (showMode: ShowMode) => {
      // @ts-ignore
      const num = RomanNumMap[++romanNum] || ''
      const data = thinkTitleMap(true)[showMode]
      return {
        thinkTitle: `${data.title}`,
        thinkingText: `${data.part}`,
      }
    }

    const findType = (type: SessionType) => data.list
      .filter(item => item.type === type)

    const chat = findType(SessionType.Chat)
    const copyWrite = findType(SessionType.Copyright)
    const report = findType(SessionType.Report)
    const videoUrl = findType(SessionType.Video)
    const modelUrl = findType(SessionType.Model)
    const imgUrl = findType(SessionType.Image)

    const reportStr = report.find(item => item.content)?.content || ''
    const copyWriteStr = copyWrite.find(item => item.content)?.content || ''
    createPublishTagEl(
      'By PhotoG',
      `${reportStr}\n ${copyWriteStr}`,
      imgUrl.flatMap(item => item?.originalImageUrls || []),
    )

    chat.forEach((item) => {
      if (item.reasoningContent) {
        addThink(item.reasoningContent)
      }
    })

    copyWrite.some(item => !!item.content) && chatStore.selectedMode.push('copyWrite')
    report.some(item => !!item.content) && chatStore.selectedMode.push('report')
    imgUrl.some(item => item.originalImageUrls?.length) && chatStore.selectedMode.push('img')
    videoUrl.some(item => item.videoUrls?.length) && chatStore.selectedMode.push('video')
    modelUrl.some(item => item.modelUrls?.length) && chatStore.selectedMode.push('3dModel')

    if (chatStore.selectedMode.length === 0) {
      god.messageWarn('No Data')
      setLoading(false)
      return
    }

    chatStore.openWorkflow = true

    copyWrite.forEach((item) => {
      if (item.content) {
        chatStore.copyWrite = item.content
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('copyWrite'))
      }
    })

    report.forEach((item) => {
      if (item.content) {
        chatStore.report = item.content
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('report'))
      }
    })

    videoUrl.forEach((item) => {
      if (item.videoUrls?.length > 0) {
        chatStore.videoUrl = item.videoUrls[0]
      }
    })

    modelUrl.forEach((item) => {
      if (item.modelUrls?.length > 0) {
        chatStore.modelUrl = item.modelUrls[0]
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('3dModel'))
      }
    })

    chatStore.previewMsgs = genArr<Msg>(imgUrl.length, () => genMsg({ currentTeamMember: chatStore.currentTeamMember }))
    imgUrl.forEach((item, index) => {
      if (!item.originalImageUrls?.length)
        return

      const oriUrl = item.originalImageUrls[0]
      try {
        const posterLayout = JSON.parse(item.layout) || {}
        const msg: Msg = {
          ...chatStore.previewMsgs[index],
          files: [
            {
              base64: item.composeImageUrls?.[0] || oriUrl,
              rawUrl: oriUrl,
              file: new File([], ''),
            },
          ],
          posterLayout,
        }

        chatStore.selectedMsg = msg
        chatStore.previewMsgs[index] = msg
      }
      catch (error) {
        console.log(error)
      }

      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('img'))
      }
    })

    chatStore.completeTeamMembers = ['Mary', 'Bob', 'David']
    for (const item of [
      ...chat,
      ...report,
      ...copyWrite,
      ...videoUrl,
      ...modelUrl,
      ...imgUrl,
    ]) {
      item.grokReason && addGrokReason(item.grokReason, item.type)
      toBottom()
      await wait(100)
    }

    for (const item of videoUrl) {
      if (item.reasoningContent) {
        addThink(item.reasoningContent, getThinkTop('video'), item.type)
        toBottom()
      }
    }

    function toBottom() {
      setTimeout(() => {
        ChatEventBus.emit('scrollToBottom')
      }, 100)
    }

    function addThink(
      think: string,
      thinkData: {
        thinkTitle?: string
        thinkingText?: string
      } = {},
      type?: SessionType,
    ) {
      type && addAvatar(type)
      // 这里需要setMessages，但我们没有这个函数，所以暂时注释掉
      // setMessages((prev) => {
      //   return [
      //     ...prev,
      //     genMsg({
      //       currentTeamMember: chatStore.currentTeamMember,
      //       content: think,
      //       role: Role.Assistant,
      //       type: 'thinkDone',
      //       ...thinkData,
      //     }),
      //   ]
      // })
    }

    function addGrokReason(grokReason: GrokReason[], type: SessionType) {
      if (grokReason.length <= 0)
        return

      addAvatar(type)

      // 这里需要setMessages，但我们没有这个函数，所以暂时注释掉
      // setMessages((prev) => {
      //   return [
      //     ...prev,
      //     genMsg({
      //       thinkStepDone: true,
      //       currentTeamMember: chatStore.currentTeamMember,
      //       role: Role.Assistant,
      //       type: 'step',
      //       stepData: grokReason.map(item => ({
      //         markdown: item.markdown,
      //         thinkDoneText: item.title,
      //         thinkingText: item.title,
      //       })),
      //     }),
      //   ]
      // })
    }

    function addAvatar(type: SessionType) {
      switch (type) {
        case SessionType.Copyright:
          chatStore.currentTeamMember = 'Bob'
          break
        case SessionType.Report:
          chatStore.currentTeamMember = 'Mary'
          break
        case SessionType.Image:
        case SessionType.Video:
        case SessionType.Model:
          chatStore.currentTeamMember = 'David'
          break

        default:
          break
      }
    }
  }

  // 重置函数
  function reset() {
    resetChatStore()
    // 这里需要setMessages，但我们没有这个函数，所以暂时注释掉
    // setMessages([])
  }

  // 初始加载
  useEffect(() => {
    if (isExpanded) {
      loadMore()
    }
  }, [isExpanded, loadMore])

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      {/* 项目选择下拉框 */}
      <div className="p-4 border-b border-gray-100">
        <div
          className="flex items-center justify-between p-3 rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
          style={{ background: 'linear-gradient(163deg, rgb(221, 157, 255) 0, rgb(54, 211, 255) 100%)' }}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <span className="text-sm font-semibold text-white">Project Name Goese Here</span>
          <SvgIcon
            icon="down"
            className={cn(
              "w-4 h-4 text-white transition-transform duration-200",
              isExpanded && "rotate-180"
            )}
          />
        </div>

        {/* 虚线分隔线 */}
        <div className="mt-3 border-t border-dashed border-gray-300"></div>
      </div>

      {/* 聊天记录侧边栏 - 根据展开状态显示/隐藏 */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="flex-1 overflow-hidden"
          >
            <div className="p-4">
              {/* 头部 */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">聊天记录</h3>
                <button
                  onClick={resetChat}
                  className="flex items-center gap-2 px-3 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg text-blue-600 transition-colors"
                >
                  <SvgIcon icon="plus" className="w-4 h-4" />
                  <span className="text-sm font-medium">新建聊天</span>
                </button>
          </div>

              {/* 聊天记录列表 */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {items.map((item) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={cn(
                      "flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",
                      { '!cursor-wait': loading },
                      selectedId === item.id && 'bg-blue-50 border border-blue-200'
                    )}
                    onClick={() => handleItemClick(item.id)}
                  >
                    <div className="w-12 h-12 rounded-md overflow-hidden flex-shrink-0 bg-gray-100">
                      {item.img ? (
                        <img
                          src={item.img}
                          alt={item.title}
                    className="w-full h-full object-cover"
                  />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          <SvgIcon icon="pic" className="w-6 h-6" />
                        </div>
                      )}
                </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {item.title}
                      </p>
                      <p className="text-xs text-gray-500">
                        {item.timestamp}
                      </p>
              </div>
                  </motion.div>
                ))}

                {/* 加载更多 */}
                {hasMore && (
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="w-full p-3 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {loading ? '加载中...' : '加载更多'}
                  </button>
                )}
          </div>
        </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}))

ProjectSidebar.displayName = 'ProjectSidebar'

export { ProjectSidebar }
export default ProjectSidebar

