import type { RouteObject } from 'react-router-dom'
import SuspenseContainer from '@/components/SuspenseContainer'
import { IS_PROD, IS_ZH } from '@/config'
// import { About } from '@/pages/Company/About'

import { Navigate } from 'react-router-dom'
import { About, Agent, Ask, Assets, Blog, BlogEditor, Careers, ChatV2, ChatV3, Distribution, Event, History, HomeV3, InnerPricing, Lab, Login, Models, News, NotFoundPage, Photog, Pricing, SelectableCardDebug, Trend, Video, ZhHome } from './lazyModules'

export const routes = genRoutes([
  {
    path: '/p',
    index: true,
    element: <Navigate to="/p/photog" />,
  },
  {
    path: '/p/innerPricing',
    element: <SuspenseContainer>
      <InnerPricing />
    </SuspenseContainer>,
  },
  {
    path: '/p/photog',
    element: <>
      <Photog />
    </>,
  },
  {
    path: '/p/video',
    element: <>
      <Video />
    </>,
  },
  // {
  //   path: '/p/models',
  //   element: <Models />,
  // },
  {
    path: '/p/history',
    element: (
      <History />
    ),
  },
  {
    path: '/p/assets',
    element: (
      <Assets />
    ),
  },

  {
    path: '/p/chat',
    element: (
      IS_PROD || IS_ZH
        ? <ChatV2 />
        : <ChatV2 />
    ),
  },
  {
    path: '/p/agent',
    element: (
      <Agent />
    ),
  },
  {
    path: '/p/distribution',
    element: (
      <Distribution />
    ),
  },
  {
    path: '/p/trend',
    element: (
      <Trend />
    ),
  },

  {
    path: '/p/lab',
    element: (
      <Lab />
    ),
  },

  {
    path: '/p/debug/selectable-card',
    element: (
      <SelectableCardDebug />
    ),
  },

  // {
  //   path: '/p/photoe',
  //   element: <>
  //       <Photoe />
  //   </>,
  // },

  /**
   * 临时演示
   */
  // {
  //   path: '/p/myLife',
  //   element: <>
  //     <MyLife />
  //   </>,
  // },

  {
    path: '*',
    element: (
      <NotFoundPage />
    ),
  },
])

export const businessRoutes = genRoutes([
  {
    path: '/',
    element: IS_ZH
      ? <ZhHome />
      : <HomeV3 />,
  },

  {
    path: '/company',
    element: <Navigate to="/about" />,
  },
  {
    path: '/news/:id?',
    element: <SuspenseContainer>
      <News />
    </SuspenseContainer>,
  },
  {
    path: '/about',
    element: <SuspenseContainer>
      <About />
    </SuspenseContainer>,
  },
  {
    path: '/career',
    element: <SuspenseContainer>
      <Careers />
    </SuspenseContainer>,
  },
  {
    path: '/resource',
    element: <Navigate to="/event" />,
  },
  {
    path: '/event/:id?',
    element: <SuspenseContainer>
      <Event />
    </SuspenseContainer>,
  },
  {
    path: '/blog/:id?',
    element: <SuspenseContainer>
      <Blog />
    </SuspenseContainer>,
  },
  {
    path: '/blogEditor',
    element: <SuspenseContainer>
      <BlogEditor />
    </SuspenseContainer>,
  },
  {
    path: '/ask',
    element: <SuspenseContainer>
      <Ask />
    </SuspenseContainer>,
  },
  {
    path: '/pricing',
    element: <SuspenseContainer>
      <Pricing />
    </SuspenseContainer>,
  },
  {
    path: '/login',
    element: <SuspenseContainer>
      <Login />
    </SuspenseContainer>,
  },
])

export type RoutePath =
  | typeof routes[number]['path']
  | typeof businessRoutes[number]['path']

export type RouteItem<T extends string> =
  Omit<RouteObject, 'path'>
  & {
    path: T
  }

function genRoutes<const T extends string>(routes: RouteItem<T>[]) {
  return routes
}
