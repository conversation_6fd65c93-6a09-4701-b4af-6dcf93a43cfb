import { GradientText } from '@/components/GradientText'
import { IS_ZH } from '@/config'
import { useT } from '@/hooks'
import cn from 'clsx'
import { motion } from 'framer-motion'
import React from 'react'

type WelcomeProps = {
  title?: string
  subtitle?: string
  className?: string
}

// AI团队成员数据
const aiTeamMembers = [
  {
    name: 'Research Analyst',
    icon: '🔍',
    description: 'Research Analyst'
  },
  {
    name: 'Brand Strategist',
    icon: '📊',
    description: 'Brand Strategist'
  },
  {
    name: 'Creative Director',
    icon: '🎨',
    description: 'Creative Director'
  },
  {
    name: 'Operations Manager',
    icon: '📋',
    description: 'Operations Manager'
  }
]

const Welcome = React.memo(
  ({
    title,
    subtitle,
    className,
  }: WelcomeProps) => {
    const t = useT()

    return (
      <div className={cn('text-center', className)}>
        {/* 主标题 */}
        {IS_ZH ? (
          <GradientText
            showAnimate={false}
            colors={[
              'rgb(47, 161, 238) 0%',
              'rgb(36, 135, 216) 10%',
              '#3278E4 40%',
              '#2962B9 60%',
              '#225199',
            ]}
            className="text-3xl md:text-4xl !font-bold mb-6"
            style={{
              lineHeight: '50px',
            }}
          >
            {title || t('photog.welcome')}
          </GradientText>
        ) : (
          <motion.h1
            className="text-3xl text-gray-900 font-bold md:text-4xl mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            style={{
              lineHeight: '50px',
            }}
          >
            {title || t('photog.welcome')}
          </motion.h1>
        )}

        {/* AI团队成员 */}
        <motion.div
          className="flex justify-center items-center gap-6 mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {aiTeamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              className="flex flex-col items-center gap-2"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center text-2xl shadow-lg border-2 border-gray-100">
                {member.icon}
              </div>
              <span className="text-sm font-medium text-gray-700">{member.description}</span>
            </motion.div>
          ))}
        </motion.div>

        {/* 副标题 */}
        <motion.p
          className="mx-auto text-base text-gray-600 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          style={{
            lineHeight: '50px',
          }}
        >
          {subtitle || t('photog.welcome-sub')}
        </motion.p>
      </div>
    )
  },
)

Welcome.displayName = 'Welcome'

export default Welcome
