import type { PageQuery, PagerList } from '@/dts'
import type { Optional } from '@jl-org/ts-tool'
import { http, request } from '@/utils'
import { downloadByData, isStr, wait } from '@jl-org/tool'

/** 使用与 MarketApi 相同的模拟数据，但独立维护 */
const mockModules = import.meta.glob<any>('./trendTestData/step*.json')

export class TrendApi {
  /** 步骤 id */
  executionId = ''
  /** 实例 id */
  processInstanceId = ''

  static isMock = false

  static async getTreeList(): Promise<XhsCategoryReq[]> {
    return request.get('/app/xhs-category/tree')
  }

  async executeAndStream(
    data: TrendWorkflowReq,
    onMsg?: (data: TrendApiResult) => void,
  ): Promise<TrendApiResult> {
    const { promise, reject, resolve } = Promise.withResolvers<TrendApiResult>()

    if (TrendApi.isMock) {
      const { workflowStep } = data
      const mockData = await mockModules[`./trendTestData/${workflowStep}.json`]()
      const result: TrendApiResult = {
        data: {},
        allJson: mockData.allJson,
        hasError: false,
        errorMsg: '',
      }

      const waitSteps = [
        'step1',
        'step2',
        'step3',
        'step4',
        'step5',
        'step6',
        'step7',
        'step8',
      ]

      /** 用于控制打字机速度的函数 */
      const typewriterDelay = async (textLength: number) => {
        /** 根据文本长度动态调整延迟，每个字符约 1-2ms */
        const delayPerChar = 1
        const minDelay = 2
        const maxDelay = 10
        const delay = Math.min(Math.max(textLength * delayPerChar, minDelay), maxDelay)
        await wait(delay)
      }

      for (let i = 0; i < mockData.allJson.length; i++) {
        const item = mockData.allJson[i]
        if (!isStr(item) && item.__internal__event === 'Message') {
          const currentContent = result.data[item.node_title]?.content || ''
          const newContent = item.content || ''

          /** 实现打字机效果：逐字符输出 */
          if (waitSteps.includes(workflowStep)) {
            /** 逐字符输出，每次输出1-3个字符 */
            const chunkSize = 2 // 每次输出的字符数
            for (let j = 0; j < newContent.length; j += chunkSize) {
              const chunk = newContent.slice(j, Math.min(j + chunkSize, newContent.length))
              result.data[item.node_title] = {
                type: 'success',
                content: currentContent + newContent.slice(0, j + chunk.length),
                msg: item,
                finished: item.node_is_finish === true, // 检查是否完成
              }
              onMsg?.(result)
              await typewriterDelay(chunk.length)
            }
          }
          else {
            /** 非等待步骤直接输出 */
            result.data[item.node_title] = {
              type: 'success',
              content: currentContent + newContent,
              msg: item,
              finished: item.node_is_finish === true, // 检查是否完成
            }
          }
          
          // 如果这是最后一条消息且标记为完成
          if (item.node_is_finish === true) {
            console.log('节点完成:', item.node_title)
            result.data[item.node_title].finished = true
            onMsg?.(result)
          }
        }
        else if (!isStr(item) && item.__internal__event === 'Message_replace') {
          const replaceContent = item.content || ''

          // Message_replace 也实现打字机效果
          if (waitSteps.includes(workflowStep)) {
            const chunkSize = 2  // 逐字符输出
            for (let j = 0; j < replaceContent.length; j += chunkSize) {
              const chunk = replaceContent.slice(j, Math.min(j + chunkSize, replaceContent.length))
              result.data[item.node_title] = {
                type: 'success',
                content: replaceContent.slice(0, j + chunk.length),
                msg: item,
              }
              onMsg?.(result)
              await typewriterDelay(chunk.length)
            }
          }
          else {
            result.data[item.node_title] = {
              type: 'success',
              content: replaceContent,
              msg: item,
            }
          }
        }
        else if (!isStr(item) && item.__internal__event === 'Node_finish') {
          result.data[item.node_title] = {
            type: 'success',
            content: result.data[item.node_title]?.content || '',
            msg: item,
            finished: true,
          }
          console.log('Node_finish事件:', item.node_title)
          onMsg?.(result)
        }
        else if (!isStr(item) && item.__internal__event === 'Error') {
          result.data[item.node_title] = {
            type: 'error',
            content: result.data[item.node_title]?.content || '',
            msg: item,
          }
          result.hasError = true
          result.errorMsg = item.content || ''
        }

      }

      // 确保最后一次调用onMsg，传递最终状态
      console.log('Mock数据流式结束，最终状态:', {
        brand_report_finished: result.data.brand_report?.finished,
        industry_report_finished: result.data.industry_report?.finished,
      })
      onMsg?.(result)
      
      resolve(result)
      return promise
    }

    const url = `/api-node/trend/workflow/execute/stream`
    const sse = http.sse<TrendWorkflowApiResponse>(url, data)

    sse.addEventListener('message', (response) => {
      const result: TrendApiResult = {
        data: {},
        allJson: response.allJson,
        hasError: false,
        errorMsg: '',
      }

      for (let i = 0; i < response.allJson.length; i++) {
        const item = response.allJson[i]
        if (!isStr(item) && item.__internal__event === 'Message') {
          const currentContent = result.data[item.node_title]?.content || ''
          const content = currentContent + (item.content || '')
          result.data[item.node_title] = {
            type: 'success',
            content,
            msg: item,
          }
        }
        else if (!isStr(item) && item.__internal__event === 'Message_replace') {
          result.data[item.node_title] = {
            type: 'success',
            content: item.content || '',
            msg: item,
          }
        }
        else if (!isStr(item) && item.__internal__event === 'Node_finish') {
          result.data[item.node_title] = {
            type: 'success',
            content: result.data[item.node_title]?.content || '',
            msg: item,
            finished: true,
          }
        }
        else if (!isStr(item) && item.__internal__event === 'Error') {
          result.data[item.node_title] = {
            type: 'error',
            content: result.data[item.node_title]?.content || '',
            msg: item,
          }
          result.hasError = true
          result.errorMsg = item.content || ''
        }
      }

      if (response.execution_id) {
        this.executionId = response.execution_id
      }
      if (response.process_instance_id) {
        this.processInstanceId = response.process_instance_id
      }

      onMsg?.(result)
    })

    sse.addEventListener('close', () => {
      resolve({
        data: {},
        allJson: [],
        hasError: false,
        errorMsg: '',
      })
      sse.close()
    })

    return promise
  }

  static getHistoryRecord(processInstanceId: string) {
    return request.post('/app/trend/history/record', {
      processInstanceId,
    })
  }

  static getHistoryList(data: PageQuery) {
    return request.post<PagerList<TrendHistoryItem>>('/app/trend/history/list', data)
  }

  static deleteHistory(processInstanceId: string) {
    return request.post('/app/trend/history/delete', {
      processInstanceId,
    })
  }

  static download(processInstanceId: string) {
    return request.post(
      '/app/trend/download',
      { processInstanceId },
      { responseType: 'arraybuffer' },
    ).then((res) => {
      return downloadByData(res, '趋势分析报告.docx')
    })
  }

  updateRelatedMarketReport(data: { params: Record<string, string> }) {
    // Mock implementation for trend API
    console.log('Updating trend report:', data)
    return Promise.resolve()
  }

  static async listXhsHistoryDetail(data: { processInstanceId: string, page: number, size: number }) {
    // This is a compatibility method for trend
    return {
      list: [],
      total: 0,
    }
  }
}

/** 从 MarketApi 复制的类型定义，但改为 Trend 前缀 */
export type TrendStep1Params = {
  brand: string
  product_name: string
  industry: string
  industry_id: number
  competitor: string
  product: string
  role: string
  company: string
  pic: string
  ip: string
  marketing_strategy: string
  product_market: string
  competitor_info: string
}

export type XhsCategoryReq = {
  label: string
  tagId: string
  subTagList?: XhsCategoryReq[]
}

export type TrendWorkflowReq = {
  workflowStep: string
  input?: any
  channel?: 'xhs'
  executionId?: string
  processInstanceId?: string
}

export type TrendWorkflowApiResponse = {
  allJson: any[]
  execution_id?: string
  process_instance_id?: string
}

export type TrendApiResult = {
  data: Record<string, {
    type: 'success' | 'error'
    content: string
    msg: any
    finished?: boolean
  }>
  allJson: any[]
  hasError: boolean
  errorMsg: string
}

export type TrendHistoryItem = {
  processInstanceId: string
  createdAt: string
  updatedAt: string
  workflowStep: string
  input: any
  output: any
}

export type TrendHistoryPageResp = {
  workflowStep: string
  role: string
  contentPayload: any
}

export type Step8Result = {
  image?: { content: string | string[] }
  content?: { content: string }
  title?: { content: string }
}
