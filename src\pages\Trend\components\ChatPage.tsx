import type { MarketStep1Params } from '@/api/MarketApi'
import type { TrendStep1Params } from '@/api/TrendApi'
import type { MdToCodePreviewType, MessageStoreType, ReportStoreType, StateStoreType, StepStateType, TaskStoreType } from '../stores'

import { useBindWinEvent } from '@/hooks'
import { cn } from '@/utils'
import { motion } from 'framer-motion'
import { memo, useEffect, useInsertionEffect, useRef, useState } from 'react'

import ChatWorkflow from '../../ChatV2/components/ChatWorkflow'
import TopBar from '../../ChatV2/components/TopBar'
import { removeMessage } from '../stores/create'
import { ChatHistory } from './ChatHistory'
import { ReportPreview } from './ReportComponents/ReportPreview'

export const ChatPage = memo<ChatPageProps>((
  {
    style,
    className,
    taskStore,
    stateStore,
    messageStore,
    mdToCodePreview,
    resetDistributionStore: _resetDistributionStore,
    reportStore,
    stepState,

    // TopBar 相关 props
    showTopBar = false,
    topBarAgents = [],
    onTopBarAgentClick,
    topBarDropdownExpanded = false,
    onTopBarDropdownToggle,

    // ChatV2 流程相关 props
    onStartAIAnalysis,
  },
) => {
  const { formData, isReportOpen, chatV2FlowMode, userDescription, uploadedImage } = stateStore.use()
  taskStore.use() // 保持响应式连接

  const chatHistoryRef = useRef<{ scrollToBottom: () => void }>(null)

  // ChatV2 流程状态管理
  const [isThinking, setIsThinking] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showWorkflow, setShowWorkflow] = useState(false)
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [showAnalysisResults, setShowAnalysisResults] = useState(false) // 新增：控制是否显示分析结果

  /** 表单状态管理 */
  const [formDataLocal, setFormDataLocal] = useState<Partial<MarketStep1Params>>({
    brand: '',
    product_name: '',
    industry: '',
    competitor: '',
    product: userDescription || '',
    pic: uploadedImage || '',
    industry_id: 83, // 默认值
    ip: '', // 默认值
    role: '', // 默认值
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  /** 处理 ChatV2 流程状态变化 */
  useEffect(() => {
    if (chatV2FlowMode === 'thinking') {
      /** 启动 Thinking 流程 */
      setIsThinking(true)
      setTimeout(() => {
        setIsThinking(false)
        setShowForm(true)
        stateStore.chatV2FlowMode = 'form'
      }, 3000)
    }
    else if (chatV2FlowMode === 'form') {
      setShowForm(true)
    }
    else if (chatV2FlowMode === 'workflow') {
      setShowForm(false)
      setShowWorkflow(true)
    }
  }, [chatV2FlowMode, stateStore])

  /** 当分析结果开始显示时，自动滚动到底部 */
  useEffect(() => {
    if (showAnalysisResults) {
      /** 使用 requestAnimationFrame 确保 DOM 已更新 */
      requestAnimationFrame(() => {
        /** 第一次滚动：确保容器可见 */
        const container = document.querySelector('.ChatPageContainer .overflow-auto')
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }

        /** 延迟后再次滚动：确保内容加载完成 */
        setTimeout(() => {
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth',
            })
          }
          /** ChatHistory 内部滚动 */
          chatHistoryRef.current?.scrollToBottom?.()
        }, 300)
      })
    }
  }, [showAnalysisResults])

  /** 表单处理函数 */
  const handleFormChange = (field: keyof MarketStep1Params, value: string) => {
    setFormDataLocal(prev => ({ ...prev, [field]: value }))
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleFormSubmit = async () => {
    if (isSubmitting)
      return

    setFormErrors({})
    const errors: Record<string, string> = {}

    /** 验证必填字段 */
    const requiredFields = [
      { key: 'brand', label: 'Please enter the brand name to continue.' },
      { key: 'product_name', label: 'Please enter the product name to continue.' },
      { key: 'industry', label: 'Please enter the industry to continue.' },
      { key: 'competitor', label: 'Please enter the competitor information to continue.' },
    ]

    requiredFields.forEach(({ key, label }) => {
      const value = formDataLocal[key as keyof typeof formDataLocal]
      if (!value || (typeof value === 'string' && !value.trim())) {
        errors[key] = label
      }
    })

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)
    try {
      /** 准备完整的表单数据 */
      const completeFormData = {
        industry: formDataLocal.industry || '',
        industry_id: 83,
        product_name: formDataLocal.product_name || '',
        ip: '专研彩妆',
        brand: formDataLocal.brand || '',
        role: '中国时尚彩妆领导品牌',
        product: formDataLocal.product || '',
        pic: formDataLocal.pic || '',
        competitor: formDataLocal.competitor || '',
        company: formDataLocal.brand || '',
        marketing_strategy: '好用',
        product_market: '',
        competitor_info: '女性彩妆',
      }

      /** 保存到 stateStore 并切换到工作流模式 */
      stateStore.cacheFormData = completeFormData
      stateStore.chatV2FlowMode = 'workflow'
    }
    catch (error) {
      console.error('Form submission failed:', error)
    }
    finally {
      setIsSubmitting(false)
    }
  }

  /** 转换 MarketStep1Params 到 TrendStep1Params */
  const convertToTrendParams = (marketParams: MarketStep1Params): TrendStep1Params => {
    return {
      brand: marketParams.brand,
      product_name: marketParams.product_name,
      industry: marketParams.industry,
      industry_id: marketParams.industry_id,
      competitor: marketParams.competitor || '',
      product: marketParams.product,
      role: marketParams.role,
      company: marketParams.company || marketParams.brand,
      pic: marketParams.pic,
      ip: marketParams.ip,
      marketing_strategy: marketParams.marketing_strategy || '',
      product_market: marketParams.product_market || '',
      competitor_info: marketParams.competitor_info || '',
    }
  }

  // Approve 按钮处理函数
  const handleApprove = async () => {
    try {
      /** 不再清除 chatV2FlowMode，保持工作流程显示 */
      /** 显示分析结果区域 */
      setShowAnalysisResults(true)

      /** 调用外部传入的 AI 分析函数 */
      if (onStartAIAnalysis && stateStore.cacheFormData) {
        const trendParams = convertToTrendParams(stateStore.cacheFormData as MarketStep1Params)

        /** 开始分析后持续监听消息变化并滚动 */
        const scrollInterval = setInterval(() => {
          const container = document.querySelector('.ChatPageContainer .overflow-auto')
          if (container) {
            const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
            /** 只有在接近底部时才自动滚动，避免干扰用户手动滚动 */
            if (isNearBottom) {
              container.scrollTo({
                top: container.scrollHeight,
                behavior: 'smooth',
              })
            }
          }
        }, 500)

        await onStartAIAnalysis(trendParams)

        /** 分析完成后清理定时器 */
        setTimeout(() => clearInterval(scrollInterval), 2000)
      }
    }
    catch (error) {
      console.error('Approve failed:', error)
    }
  }

  useBindWinEvent('resize', () => {
    stateStore.isReportOpen = false
    stateStore.isAgentCollapsed = true
  })

  /** 当页面加载且有表单数据时，创建一个开始按钮让用户手动启动 */
  useEffect(() => {
    if (!formData) {
      return
    }

    /** 创建一个初始任务，让用户点击开始 */
    if (taskStore.agentTasks.length === 0) {
      taskStore.agentTasks.push({
        id: 'start',
        title: 'Marketing Team Ready',
        description: '点击下方按钮开始执行营销方案生成',
        status: 'waiting',
        actions: [{ label: '开始执行 Research Analyst', type: 'primary' }],
        step: 'step0' as any,
      })
    }
  }, [formData, taskStore.agentTasks])

  useInsertionEffect(() => {
    const overflow = document.body.style.overflow
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = overflow
    }
  }, [])

  return <div
    className={ cn(
      'ChatPageContainer flex flex-row h-full overflow-hidden p-4 gap-4',
      className,
    ) }
    style={ style }
  >
    {/* 主内容区 */}
    <div className="flex flex-1 flex-col overflow-hidden rounded-2xl bg-white">
      {/* TopBar - 只在左侧聊天区域上方显示 */}
      {showTopBar && (
        <div className="flex-shrink-0 border-b border-gray-100">
          <TopBar
            agents={ topBarAgents }
            onAgentClick={ onTopBarAgentClick }
            dropdownExpanded={ topBarDropdownExpanded }
            onDropdownToggle={ onTopBarDropdownToggle }
            containerClassName="relative"
          />
        </div>
      )}

      {/* 聊天内容区域 */}
      <div className="flex-1 overflow-hidden">
        {chatV2FlowMode
          ? (
            // ChatV2 流程界面 - 使用可滚动容器
              <div className="relative h-full flex flex-col">
                <div className="flex-1 overflow-auto pb-22">
                  {' '}
                  {/* 为底部输入框预留空间 */}
                  <div className="mx-auto max-w-4xl p-4">
                    <ChatWorkflow
                      showContentDisplay
                      content={ userDescription }
                      uploadedImage={ uploadedImage }
                      showThinking
                      isThinking={ isThinking }
                      thinkingExpanded={ thinkingExpanded }
                      onThinkingToggle={ setThinkingExpanded }
                      showWorkflow={ showWorkflow }
                      showForm={ showForm && !showWorkflow }
                      formData={ formDataLocal }
                      formErrors={ formErrors }
                      formUploadedImage={ formDataLocal.pic }
                      isSubmitting={ isSubmitting }
                      onFormChange={ handleFormChange }
                      onFormSubmit={ handleFormSubmit }
                      onImageUpload={ (file) => {
                        const reader = new FileReader()
                        reader.onload = (e) => {
                          const result = e.target?.result as string
                          handleFormChange('pic', result)
                        }
                        reader.readAsDataURL(file)
                      } }
                      onImageRemove={ () => handleFormChange('pic', '') }
                      workflowTitle="Your AI Agent Team's Action plan"
                      workflowDescription="Perfect! Your brand profile is ready. Now I'm bringing together my AI agent team to create amazing trend analysis for you. Here's how we'll work together:"
                      showAskInput={ false } // 禁用内置的 Ask 输入框
                      askInputPlaceholder=""
                      askInputValue=""
                      askInputDisabled
                      onAskInputSubmit={ () => {} }
                    />

                    {/* 分析结果区域 - 直接衔接在工作流程后面 */}
                    {showAnalysisResults && (
                      <motion.div
                        className="mt-6 border-gray-200 pt-6"
                        initial={ { opacity: 0, y: 20 } }
                        animate={ { opacity: 1, y: 0 } }
                        transition={ { duration: 0.5 } }
                      >
                        <ChatHistory
                          taskStore={ taskStore }
                          messageStore={ messageStore }
                          ref={ chatHistoryRef }
                          className="min-h-0 w-full"
                          onDeleteMessage={ removeMessage }
                          stateStore={ stateStore }
                        />
                      </motion.div>
                    )}
                  </div>
                </div>

                {/* 悬浮的输入框 - 始终显示在底部 */}
                {showWorkflow && (
                  <div className="absolute bottom-0 left-0 right-0 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          value={ showAnalysisResults
                            ? 'AI analysis in progress...'
                            : 'Ready to start trend analysis' }
                          readOnly
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                          placeholder={ showAnalysisResults
                            ? 'Analysis running...'
                            : 'Click the arrow to approve and start AI analysis...' }
                        />
                        {!showAnalysisResults && (
                          <button
                            onClick={ handleApprove }
                            className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white transition-colors hover:bg-blue-600"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                          </button>
                        )}
                        {showAnalysisResults && (
                          <div className="h-8 w-8 flex items-center justify-center">
                            <div className="h-4 w-4 animate-spin border-b-2 border-blue-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )
          : (
            /** 正常的聊天界面 */
              <div className="relative h-full flex flex-col">
                <motion.div
                  layout
                  className={ cn(
                    'flex-1 flex flex-col gap-4 max-w-4xl mx-auto overflow-auto',
                    isReportOpen && 'max-w-3xl',
                  ) }>

                  <ChatHistory
                    taskStore={ taskStore }
                    messageStore={ messageStore }
                    ref={ chatHistoryRef }
                    className="min-h-0 w-full flex-1 p-4"
                    onDeleteMessage={ removeMessage }
                    stateStore={ stateStore }
                  />
                </motion.div>

                {/* 预留悬浮输入框位置 - 目前不显示 */}
                {false && (
                  <div className="absolute bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          placeholder="Type your message..."
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                        />
                        <button className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white transition-colors hover:bg-blue-600">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
      </div>
    </div>

    <ReportPreview
      isOpen={ isReportOpen }
      onClose={ () => stateStore.isReportOpen = !stateStore.isReportOpen }
      className="flex-shrink-0"
      reportStore={ reportStore }
      taskStore={ taskStore }
      stepState={ stepState }
      mdToCodePreview={ mdToCodePreview }
      stateStore={ stateStore }
    />
  </div>
})

ChatPage.displayName = 'ChatPage'

export type ChatPageProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode

  taskStore: TaskStoreType
  stateStore: StateStoreType
  messageStore: MessageStoreType
  mdToCodePreview: MdToCodePreviewType
  resetDistributionStore: () => void
  reportStore: ReportStoreType
  stepState: StepStateType

  // TopBar 相关 props
  showTopBar?: boolean
  topBarAgents?: any[]
  onTopBarAgentClick?: (agent: any) => void
  topBarDropdownExpanded?: boolean
  onTopBarDropdownToggle?: (expanded: boolean) => void

  // ChatV2 流程相关 props
  onStartAIAnalysis?: (formData: any) => Promise<void>
}
& React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLDivElement>, HTMLDivElement>
