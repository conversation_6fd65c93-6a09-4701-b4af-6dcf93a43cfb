import type { Trend<PERSON><PERSON><PERSON><PERSON><PERSON>, TrendStep1Params, TrendWorkflowReq } from '@/api/TrendApi'
import type { PartRequired } from '@jl-org/ts-tool'
import type { AgentTask, TaskAction } from '../types'
import { excludeKeys, filterKeys, uniqueId, wait } from '@jl-org/tool'
import { DistributionEvent, eventBus, trendApi } from '../constants'
import { addReportByTitle, createReportCard } from './addChat'
import { createAgentTask, createThink, thinkEnd, updateAgentTask, updateAgentTaskStatus, updateMessageById } from './create'
import { handleTrendAgentError } from './error'
import { getNextStep } from './fns'
import { handleStep3, handleStep6, handleStep7, handleStep8 } from './handleStepV2'
import { trendAg } from './index'

// eslint-disable-next-line import/no-mutable-exports
export let cacheExecuteAndStream = async (
  data: PartRequired<TrendWorkflowReq, 'workflowStep'>,
  onMsg?: (data: TrendApiResult) => void,
): Promise<TrendApiResult> => {
  return {} as any
}

export async function handleFormSubmit(params: TrendStep1Params, store = trendAg): Promise<void> {
  const { messageStore, stateStore, stepState, mdToCodePreview, reportStore, taskStore } = store

  let isFirst = true
  const step = getNextStep(store).curStep
  const think = createThink('', { meta: { step } })
  messageStore.messages.push(think)  // 立即显示thinking，但内容会流式更新

  createAgentTask(undefined, step, store)
  
  /** 触发滚动到底部事件 */
  setTimeout(() => {
    eventBus.emit(DistributionEvent.ScrollToBottom)
  }, 100)

  trendApi.executionId = ''
  trendApi.processInstanceId = ''

  stateStore.cacheFormData = excludeKeys(params, ['pic'])

  cacheExecuteAndStream = async (data, onMsg) => {
    stateStore.isLoading = true
    const msg = await trendApi.executeAndStream(
      {
        ...data,
        params: data.workflowStep === 'step1'
          ? {
              ...params,
              ...data.params,
            }
          : {
              ...stateStore.cacheFormData,
              ...data.params,
            },
      },
      (messages) => {
        onMsg?.(messages)
      },
    )
      .finally(() => {
        stateStore.isLoading = false
      })

    if (msg.hasError) {
      stateStore.errorMsg = msg.errorMsg
      stateStore.hasError = true
    }
    return msg
  }

  stateStore.isProcessing = true
  stepState.step1.isProcessing = true
  const titles = ['brand_report', 'industry_report']
  const thinks = ['brand_reasoning_content', 'industry_reasoning_content']

  mdToCodePreview.brand_report.mdIsProcessing = true
  mdToCodePreview.industry_report.mdIsProcessing = true

  // 标记是否已经处理过完成状态
  let hasProcessedComplete = false

  const { hasError } = await cacheExecuteAndStream(
    {
      processInstanceId: '',
      workflowStep: step,
    },
    async (res) => {
      // 第一次收到数据时设置为进行中
      if (isFirst && Object.keys(res.data).length > 0) {
        updateAgentTaskStatus(step, 'in-progress', store)
        isFirst = false
      }
      
      addReportByTitle(filterKeys(res.data, titles), {
        type: 'markdown',
        meta: {
          step,
          canTransformCode: true,
        },
      }, false, store)  // 改为 false，不自动打开报告面板

      let thinkContent = ''
      const thinkingData = filterKeys(res.data, thinks)
      Object.entries(thinkingData).forEach(([k, v]) => {
        thinkContent += v.content
      })

      updateMessageById(think.id, {
        content: thinkContent,
      }, store)  // 实时更新thinking消息内容
      
      /** 流式内容更新时触发滚动 */
      eventBus.emit(DistributionEvent.ScrollToBottom)
      
      // 检查是否有任何thinking内容，如果有就结束thinking
      if (thinkContent && !think.type.includes('end')) {
        thinkEnd(think.id, undefined, store)
      }
      
      // 检查报告节点是否完成
      let allReportsFinished = true
      titles.forEach(title => {
        if (!res.data[title] || !res.data[title].finished) {
          allReportsFinished = false
        }
      })
      
      // 只在状态变化时输出日志
      if (res.data.brand_report?.finished || res.data.industry_report?.finished) {
        console.log('报告完成状态更新:', {
          brand_report: res.data.brand_report?.finished || false,
          industry_report: res.data.industry_report?.finished || false,
          allFinished: allReportsFinished,
          timestamp: new Date().toISOString()
        })
      }
      
      // 如果所有报告都完成了，且还没处理过
      if (allReportsFinished && !hasProcessedComplete) {
        hasProcessedComplete = true
        
        // 立即设置为complete状态
        updateAgentTaskStatus(step, 'complete', store)
        
        // 延迟1秒后显示报告卡片
        setTimeout(() => {
          createReportCard(reportStore.items.filter(item => item.meta.step === step), step, store)
          taskStore.currentStep = step
          updateAgentTask(step, {
            actions: [{ label: 'Approve', type: 'primary' }],
          }, store)
        }, 1000)
      }
    },
  )
    .finally(() => {
      stateStore.isLoading = false
    })

  if (hasError) {
    handleAgentError(true, store)
    return
  }

  stepState.step1.isProcessing = false
  mdToCodePreview.brand_report.mdIsProcessing = false
  mdToCodePreview.industry_report.mdIsProcessing = false
}

export async function handleTaskAction(action: TaskAction, task: AgentTask, store = trendAg) {
  const { stateStore, stepState, taskStore } = store

  if (stateStore.hasError) {
    return
  }

  const step = task.step
  
  // 处理初始的开始按钮
  if (action.label?.startsWith('开始执行 Research Analyst')) {
    // 清空初始任务
    taskStore.agentTasks = []
    // 执行第一个机器人
    const formData = stateStore.formData
    if (formData) {
      await handleFormSubmit(formData, store)
      stateStore.formData = null
    }
    return
  }
  
  // 如果是"Approve"按钮，只是完成当前步骤并显示"执行下一步"按钮
  if (action.label === 'Approve') {
    switch (step) {
      case 'step1':
        // 更新当前任务，显示"执行下一步"按钮
        updateAgentTask(step, {
          actions: [{ label: '执行 Brand Strategist', type: 'success' }],
        }, store)
        break
      
      case 'step2':
        updateAgentTask(step, {
          actions: [{ label: '执行 Creative Director', type: 'success' }],
        }, store)
        break
        
      case 'step3':
        updateAgentTask(step, {
          actions: [{ label: '执行 Content Manager', type: 'success' }],
        }, store)
        break
        
      case 'step4':
        // 最后一步，不需要下一步按钮
        stateStore.isProcessing = false
        break
    }
    return
  }
  
  // 如果是"执行下一步"按钮，执行对应的步骤
  if (action.label?.startsWith('执行')) {
    switch (step) {
      case 'step1':
        stateStore.isReportOpen = false
        stepState.step2.isProcessing = true
        await handleStep3(store)
        stepState.step2.isProcessing = false
        break

      case 'step2':
        stateStore.isReportOpen = false
        stepState.step3.isProcessing = true
        await handleStep6(store)
        stepState.step3.isProcessing = false
        break

      case 'step3':
        stateStore.isReportOpen = false
        stepState.step4.isProcessing = true
        await handleStep7(store)
        stepState.step4.isProcessing = false
        break

      case 'step4':
        stateStore.isReportOpen = false
        await handleStep8(store)
        stateStore.isProcessing = false
        break

      default:
        break
    }
  }
}

export function handleTaskClick(task: AgentTask, taskStore = trendAg.taskStore) {
  taskStore.currentStep = task.step
  eventBus.emit(DistributionEvent.ScrollToBottom)
}
